using SRJ.Logger;
using System.Diagnostics;
using System.Text;

namespace SRJ.API.Middlewares
{
    public class RequestResponseLoggingMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILoggerManager _logger;
        private readonly IConfiguration _configuration;

        public RequestResponseLoggingMiddleware(RequestDelegate next, ILoggerManager logger, IConfiguration configuration)
        {
            _next = next;
            _logger = logger;
            _configuration = configuration;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            var stopwatch = Stopwatch.StartNew();
            var requestId = Guid.NewGuid().ToString();
            
            // Add request ID to context for correlation
            context.Items["RequestId"] = requestId;
            
            // Log request
            await LogRequestAsync(context, requestId);
            
            // Capture response
            var originalResponseBodyStream = context.Response.Body;
            using var responseBodyStream = new MemoryStream();
            context.Response.Body = responseBodyStream;
            
            try
            {
                await _next(context);
            }
            finally
            {
                stopwatch.Stop();
                
                // Log response
                await LogResponseAsync(context, requestId, stopwatch.ElapsedMilliseconds);
                
                // Copy response back to original stream
                responseBodyStream.Seek(0, SeekOrigin.Begin);
                await responseBodyStream.CopyToAsync(originalResponseBodyStream);
            }
        }

        private async Task LogRequestAsync(HttpContext context, string requestId)
        {
            try
            {
                var request = context.Request;
                var requestBody = await ReadRequestBodyAsync(request);
                
                var logData = new
                {
                    RequestId = requestId,
                    Timestamp = DateTime.UtcNow,
                    Method = request.Method,
                    Path = request.Path.Value,
                    QueryString = request.QueryString.Value,
                    Headers = GetSafeHeaders(request.Headers),
                    Body = ShouldLogBody() ? requestBody : "[Body logging disabled]",
                    UserAgent = request.Headers.UserAgent.ToString(),
                    RemoteIpAddress = context.Connection.RemoteIpAddress?.ToString(),
                    UserId = context.User?.Identity?.Name ?? "Anonymous"
                };

                _logger.LogInfo($"API Request: {System.Text.Json.JsonSerializer.Serialize(logData)}");
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error logging request: {ex.Message}");
            }
        }

        private async Task LogResponseAsync(HttpContext context, string requestId, long elapsedMilliseconds)
        {
            try
            {
                var response = context.Response;
                var responseBody = await ReadResponseBodyAsync(response);
                
                var logData = new
                {
                    RequestId = requestId,
                    Timestamp = DateTime.UtcNow,
                    StatusCode = response.StatusCode,
                    Headers = GetSafeHeaders(response.Headers),
                    Body = ShouldLogBody() && ShouldLogResponseBody(response.StatusCode) ? responseBody : "[Body logging disabled]",
                    ElapsedMilliseconds = elapsedMilliseconds,
                    ContentType = response.ContentType,
                    ContentLength = response.ContentLength
                };

                var logLevel = GetLogLevel(response.StatusCode);
                var message = $"API Response: {System.Text.Json.JsonSerializer.Serialize(logData)}";
                
                switch (logLevel)
                {
                    case "Error":
                        _logger.LogError(message);
                        break;
                    case "Warn":
                        _logger.LogWarn(message);
                        break;
                    default:
                        _logger.LogInfo(message);
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error logging response: {ex.Message}");
            }
        }

        private async Task<string> ReadRequestBodyAsync(HttpRequest request)
        {
            if (!request.Body.CanSeek)
            {
                request.EnableBuffering();
            }

            request.Body.Position = 0;
            var reader = new StreamReader(request.Body, Encoding.UTF8);
            var body = await reader.ReadToEndAsync();
            request.Body.Position = 0;
            
            return body;
        }

        private async Task<string> ReadResponseBodyAsync(HttpResponse response)
        {
            response.Body.Seek(0, SeekOrigin.Begin);
            var reader = new StreamReader(response.Body, Encoding.UTF8);
            var body = await reader.ReadToEndAsync();
            response.Body.Seek(0, SeekOrigin.Begin);
            
            return body;
        }

        private Dictionary<string, string> GetSafeHeaders(IHeaderDictionary headers)
        {
            var safeHeaders = new Dictionary<string, string>();
            var sensitiveHeaders = new[] { "authorization", "cookie", "x-api-key", "x-auth-token" };
            
            foreach (var header in headers)
            {
                var key = header.Key.ToLowerInvariant();
                if (sensitiveHeaders.Contains(key))
                {
                    safeHeaders[header.Key] = "[REDACTED]";
                }
                else
                {
                    safeHeaders[header.Key] = string.Join(", ", header.Value);
                }
            }
            
            return safeHeaders;
        }

        private bool ShouldLogBody()
        {
            return _configuration.GetValue<bool>("Logging:LogRequestResponseBodies", false);
        }

        private bool ShouldLogResponseBody(int statusCode)
        {
            // Don't log response body for successful file downloads or large responses
            return statusCode < 300 || statusCode >= 400;
        }

        private string GetLogLevel(int statusCode)
        {
            return statusCode switch
            {
                >= 500 => "Error",
                >= 400 => "Warn",
                _ => "Info"
            };
        }
    }
}
