﻿using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using SRJ.DataAccess.Interfaces.MyFatoorah;
using SRJ.CommonCore.SettingsModels;
using SRJ.DataAccess;
using SRJ.DataAccess.Common;
using SRJ.DataAccess.Entities;
using SRJ.CommonCore.ViewModels;
using System.Net.Http.Headers;
using System.Net.Http;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;
using SRJ.CommonCore.Services.Resilience;
using SRJ.CommonCore.Interfaces;

namespace SRJ.CommonCore.Services.MyFatoorah
{
    public class FatoorahService<TEntity, TViewModel> : IFatoorahService<TEntity, TViewModel>
        where TEntity : BaseEntity
        where TViewModel : class, IPaymentBaseEntity
    {
        private readonly IOptions<MyFatoorahSettings> _config;
        private readonly ApplicationDbContext _context;
        private readonly ICustomerReferenceService _customerReferenceService;
        private readonly HttpClient _httpClient;
        private readonly IGenericService<PaymentLogTable, PaymentLogTableVM> _paymentLogService;
        private readonly IResilienceService _resilienceService;

        public FatoorahService(
            IOptions<MyFatoorahSettings> config,
            ApplicationDbContext context,
            ICustomerReferenceService customerReferenceService,
            IHttpClientFactory httpClientFactory,
            IGenericService<PaymentLogTable, PaymentLogTableVM> paymentLogService,
            IResilienceService resilienceService)
        {
            _config = config;
            _context = context;
            _customerReferenceService = customerReferenceService;
            _httpClient = httpClientFactory.CreateClient();
            _paymentLogService = paymentLogService;
            _resilienceService = resilienceService;
        }

        public async Task<string> Payment(TViewModel model)
        {
            var response = await SendPayment(model);
            return response;
        }

        public async Task<string> SendPayment(TViewModel model)
        {
            string Id = string.IsNullOrEmpty(model.Email) ? new Random().Next(100, 1000).ToString() : model.Email.Split("@")[0];
            string refr = _customerReferenceService.GenerateCustomerReference(Id, CustomerReferenceType.STU);

            var itemDetails = JObject.Parse(model.ItemName);
            var items = itemDetails["items"].ToString();

            var sendPaymentRequest = new Dictionary<string, object>
            {
                { "CustomerName", model.Name },
                { "NotificationOption", "LNK" },
                { "InvoiceValue", model.Cost },
                { "DisplayCurrencyIso", "KWD" },
                { "MobileCountryCode", "965" },
                { "CustomerMobile", model.MobileNumber },
                { "CallBackUrl", _config.Value.CallBackUrl },
                { "ErrorUrl", _config.Value.ErrorUrl },
                { "Language", "En" },
                { "CustomerReference", refr },
                { "CustomerCivilId", string.IsNullOrEmpty(model.CivilId) ? "" : model.CivilId },
                { "UserDefinedField", model.ItemName },
                { "ExpiryDate", DateTime.Now.AddHours(24) },
                { "InvoiceItems", new List<Dictionary<string, object>> { new Dictionary<string, object> {
                    { "ItemName",items }, { "Quantity", 1 }, { "UnitPrice", model.Cost }
                }}}
            };

            // Conditionally add the CustomerEmail field
            if (IsValidEmail(model.Email))
            {
                sendPaymentRequest.Add("CustomerEmail", model.Email);
            }

            var sendPaymentRequestJSON = JsonConvert.SerializeObject(sendPaymentRequest);
            var response = await PerformRequest(sendPaymentRequestJSON, "SendPayment");

            await LogPaymentAttempt(model, sendPaymentRequestJSON, response, refr);

            return response;
        }

        private async Task LogPaymentAttempt(TViewModel model, string requestJSON, string response, string refr)
        {
            var responseObject = JObject.Parse(response);

            var isSuccess = responseObject["IsSuccess"]?.Value<bool>() ?? false;
            var message = responseObject["Message"]?.ToString();
            var validationErrors = responseObject["ValidationErrors"]?.ToString();
            var data = responseObject["Data"];

            var invoiceId = data?["InvoiceId"]?.ToString();
            var invoiceUrl = data?["InvoiceURL"]?.ToString();
            var invoiceUrlId = invoiceUrl?.Split('/').Last();

            var paymentLog = new PaymentLogTableVM
            {
                ktechId = model.CivilId,
                IsSuccess = isSuccess ? "true" : "false",
                Message = message,
                ValidationErrors = isSuccess ? "None" : validationErrors,
                InvoiceId = invoiceId,
                InvoiceURL = invoiceUrl,
                CustomerReference = refr,
                InvoiceURLId = invoiceUrlId,
            };

            await _paymentLogService.CreateAsync("", paymentLog);
        }

        public async Task<string> GetPaymentStatus(string paymentId)
        {
            string keyType = "PaymentId";
            var getPaymentStatusResponse = await PerformRequest(JsonConvert.SerializeObject(new { Key = paymentId, KeyType = keyType }), "GetPaymentStatus");
            return getPaymentStatusResponse;
        }

        public async Task<string> GetPaymentStatusByInvoiceId(string invoiceId)
        {
            string keyType = "InvoiceId";
            var getPaymentStatusResponse = await PerformRequest(JsonConvert.SerializeObject(new { Key = invoiceId, KeyType = keyType }), "GetPaymentStatus");
            return getPaymentStatusResponse;
        }

        public async Task<string> PerformRequest(string requestJSON, string endPoint)
        {
            string url = _config.Value.MyfatoorahBaseUrl + $"/v2/{endPoint}";

            var responseMessage = await _resilienceService.ExecuteHttpRequestAsync(async () =>
            {
                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _config.Value.MyfatoorahApiKey);
                var httpContent = new StringContent(requestJSON, System.Text.Encoding.UTF8, "application/json");
                return await _httpClient.PostAsync(url, httpContent);
            }, "MyFatoorah");

            string response = string.Empty;
            if (!responseMessage.IsSuccessStatusCode)
            {
                response = JsonConvert.SerializeObject(new
                {
                    IsSuccess = false,
                    Message = responseMessage.StatusCode.ToString()
                });
            }
            else
            {
                response = await responseMessage.Content.ReadAsStringAsync();
            }

            return response;
        }

        private bool IsValidEmail(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return false;

            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }
    }
}