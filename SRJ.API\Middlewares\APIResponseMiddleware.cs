﻿using SRJ.Logger;
using SRJ.API.Models;
using SRJ.API.Exceptions;
using System.Net;
using System.Text.Json;
using Microsoft.AspNetCore.Mvc;

namespace SRJ.API.Middlewares
{
    public class APIResponseMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILoggerManager _logger;
        private readonly IWebHostEnvironment _environment;

        public APIResponseMiddleware(RequestDelegate next, ILoggerManager logger, IWebHostEnvironment environment)
        {
            _logger = logger;
            _next = next;
            _environment = environment;
        }

        public async Task InvokeAsync(HttpContext httpContext)
        {
            try
            {
                await _next(httpContext);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Unhandled exception occurred: {ex}");
                await HandleExceptionAsync(httpContext, ex);
            }
        }

        private async Task HandleExceptionAsync(HttpContext context, Exception exception)
        {
            context.Response.ContentType = "application/json";

            var response = exception switch
            {
                ApiException apiEx => CreateApiExceptionResponse(apiEx, context),
                ValidationException validationEx => CreateValidationExceptionResponse(validationEx, context),
                UnauthorizedAccessException => CreateUnauthorizedResponse(context),
                ArgumentException argEx => CreateBadRequestResponse(argEx.Message, context),
                InvalidOperationException invalidOpEx => CreateBadRequestResponse(invalidOpEx.Message, context),
                _ => CreateInternalServerErrorResponse(exception, context)
            };

            var jsonResponse = JsonSerializer.Serialize(response, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = _environment.IsDevelopment()
            });

            await context.Response.WriteAsync(jsonResponse);
        }

        private ApiResponse<object> CreateApiExceptionResponse(ApiException apiException, HttpContext context)
        {
            context.Response.StatusCode = (int)apiException.StatusCode;

            var response = ApiResponse.ErrorResponse(
                apiException.Message,
                (int)apiException.StatusCode,
                new List<string> { apiException.ErrorCode }
            );

            response.TraceId = context.TraceIdentifier;
            return response;
        }

        private ApiResponse<object> CreateValidationExceptionResponse(ValidationException validationException, HttpContext context)
        {
            context.Response.StatusCode = (int)validationException.StatusCode;

            var response = ApiResponse.ValidationErrorResponse(validationException.ValidationErrors);
            response.TraceId = context.TraceIdentifier;
            return response;
        }

        private ApiResponse<object> CreateUnauthorizedResponse(HttpContext context)
        {
            context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;

            var response = ApiResponse.ErrorResponse(
                "Unauthorized access",
                (int)HttpStatusCode.Unauthorized,
                new List<string> { "UNAUTHORIZED" }
            );

            response.TraceId = context.TraceIdentifier;
            return response;
        }

        private ApiResponse<object> CreateBadRequestResponse(string message, HttpContext context)
        {
            context.Response.StatusCode = (int)HttpStatusCode.BadRequest;

            var response = ApiResponse.ErrorResponse(
                message,
                (int)HttpStatusCode.BadRequest,
                new List<string> { "BAD_REQUEST" }
            );

            response.TraceId = context.TraceIdentifier;
            return response;
        }

        private ApiResponse<object> CreateInternalServerErrorResponse(Exception exception, HttpContext context)
        {
            context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;

            var message = _environment.IsDevelopment()
                ? exception.Message
                : "An internal server error occurred";

            var errors = new List<string> { "INTERNAL_SERVER_ERROR" };

            if (_environment.IsDevelopment() && exception.StackTrace != null)
            {
                errors.Add($"StackTrace: {exception.StackTrace}");
            }

            var response = ApiResponse.ErrorResponse(
                message,
                (int)HttpStatusCode.InternalServerError,
                errors
            );

            response.TraceId = context.TraceIdentifier;
            return response;
        }
    }
}
