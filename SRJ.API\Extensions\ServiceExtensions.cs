﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.OpenApi.Models;
using System.Text;
using Microsoft.IdentityModel.Tokens;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ApiExplorer;
using Microsoft.AspNetCore.Mvc.Versioning;
using SRJ.API.JWT;
using SRJ.API.Swagger;
using SRJ.CommonCore.Services.Resilience;
using SRJ.CommonCore.Services;
using SRJ.DataAccess;
using SRJ.DataAccess.Identity;
using SRJ.DataAccess.Interfaces.MyFatoorah;
using SRJ.CommonCore.Services.MyFatoorah;
using SRJ.CommonCore.Interfaces.Application;
using SRJ.CommonCore.Services.Application;
using SRJ.Logger;
using SRJ.CommonCore.SettingsModels;
using SRJ.CommonCore;
using SRJ.CommonCore.Mappings;
using System.Text.Json.Serialization;
using Microsoft.AspNetCore.Identity;
using System.Text.Json;
using SRJ.CommonCore.Interfaces;
using SRJ.CommonCore.Interfaces.Account;
using SRJ.CommonCore.Services.Account;
using System.Threading.RateLimiting;

namespace SRJ.API.Extensions
{
    public static class ServiceExtensions
    {
        public static void ConfigureCustomServices(this IServiceCollection services, IConfiguration configuration, IWebHostEnvironment environment)
        {
            services.ConfigureCors(configuration);
            services.ConfigureLoggerService();
            services.ConfigureMSSqlServer(configuration);
            services.ConfigureIdentity();
            services.ConfigureRepository();
            services.ConfigureSettings(configuration);
            services.ConfigureAutoMapper();
            services.AddHttpClient();
            services.ConfigureJwtAuthentication(configuration, environment);
            services.ConfigureRateLimiting();
            services.ConfigureApiVersioning();
            services.ConfigureSwagger();
            services.AddControllersWithCustomJsonOptions();
        }

        public static void ConfigureCors(this IServiceCollection services, IConfiguration configuration)
        {
            var allowedOrigins = configuration.GetSection("CorsSettings:AllowedOrigins").Get<string[]>();

            services.AddCors(options =>
            {
                options.AddPolicy("CorsPolicy",
                    builder => builder.WithOrigins(allowedOrigins)
                                      .AllowAnyHeader()
                                      .AllowAnyMethod()
                                      .AllowCredentials());
            });
        }

        public static IServiceCollection ConfigureIdentity(this IServiceCollection services)
        {
            services.AddIdentity<AppUser, AppRole>(options =>
            {
                // Enhanced password policy
                options.Password.RequiredLength = 8;
                options.Password.RequireDigit = true;
                options.Password.RequireLowercase = true;
                options.Password.RequireUppercase = true;
                options.Password.RequireNonAlphanumeric = true;
                options.Password.RequiredUniqueChars = 6;

                // Account lockout settings
                options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(15);
                options.Lockout.MaxFailedAccessAttempts = 5;
                options.Lockout.AllowedForNewUsers = true;

                // User settings
                options.User.RequireUniqueEmail = true;

                options.SignIn.RequireConfirmedAccount = false;
                options.SignIn.RequireConfirmedPhoneNumber = false;
                options.SignIn.RequireConfirmedEmail = false;

            }).AddEntityFrameworkStores<ApplicationDbContext>()
            .AddTokenProvider<DataProtectorTokenProvider<AppUser>>(TokenOptions.DefaultProvider)
            .AddDefaultTokenProviders();

            return services;
        }

        public static IServiceCollection ConfigureMSSqlServer(this IServiceCollection services, IConfiguration configuration)
        {
            var connectionString = configuration.GetConnectionString("DefaultConnection");

            services.AddDbContext<ApplicationDbContext>(options =>
            {
                options.UseSqlServer(connectionString, sqlOptions =>
                {
                    // Enable connection resiliency
                    sqlOptions.EnableRetryOnFailure(
                        maxRetryCount: 3,
                        maxRetryDelay: TimeSpan.FromSeconds(5),
                        errorNumbersToAdd: null);

                    // Set command timeout
                    sqlOptions.CommandTimeout(30);
                });

                // Enable sensitive data logging in development only
                if (configuration.GetValue<bool>("Logging:EnableSensitiveDataLogging"))
                {
                    options.EnableSensitiveDataLogging();
                }

                // Enable detailed errors in development
                if (configuration.GetValue<bool>("Logging:EnableDetailedErrors"))
                {
                    options.EnableDetailedErrors();
                }
            });

            return services;
        }

        public static IServiceCollection ConfigureMySqlContext(this IServiceCollection services, IConfiguration config)
        {
            var connectionString = config.GetConnectionString("MySqlDefaultConnection");

            services.AddDbContext<ApplicationDbContext>(opt =>
            {
                opt.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString));
            });

            return services;
        }

        public static IServiceCollection ConfigureSQLite(this IServiceCollection services, IConfiguration configuration)
        {
            var connectionString = configuration.GetConnectionString("SQLiteConnection");

            services.AddDbContext<ApplicationDbContext>(options =>
              options.UseSqlite(connectionString));

            return services;
        }

        public static void ConfigureSettings(this IServiceCollection services, IConfiguration configuration)
        {
            services.Configure<MailSettings>(configuration.GetSection("EmailSettings"));
            services.Configure<MyFatoorahSettings>(configuration.GetSection("MyFatoorahSettings"));
            services.Configure<DealApiSettings>(configuration.GetSection("DealApiSettings"));
            services.Configure<MoodleApiSettings>(configuration.GetSection("MoodleApi")); // Configuration for Moodle Api
            services.Configure<ShareFolderPUCSettings>(configuration.GetSection("ShareFolderPUCSettings"));

            services.AddHttpClient<IDealsService, DealsService>();
        }

        public static void ConfigureRepository(this IServiceCollection services)
        {
            services.AddScoped<JWTManager>();
            services.AddScoped<IUserProfileService, UserProfileService>(); // Registration of User Profile
            services.AddScoped<IDealsService, DealsService>();
            services.AddScoped(typeof(IFatoorahService<,>), typeof(FatoorahService<,>));
            services.AddScoped<IFileService, FileService>();
            services.AddScoped<IGradeReportService, GradeReportService>(); // Registration of Create Invoice Service
            services.AddScoped<ICustomerReferenceService, CustomerReferenceService>();
            services.AddScoped<IStatisticsService, StatisticsService>();
            services.AddScoped<ICreateInvoiceService, CreateInvoiceService>();
            services.AddTransient<IAccount, AccountService>();
            services.AddScoped(typeof(IGenericService<,>), typeof(GenericService<,>));

            // Configure resilience settings will be done in Program.cs

            // Register resilience service
            services.AddSingleton<IResilienceService, ResilienceService>();
        }

        public static IServiceCollection ConfigureAutoMapper(this IServiceCollection services)
        {
            services.AddAutoMapper(typeof(MappingProfile));
            return services;
        }

        public static void ConfigureLoggerService(this IServiceCollection services)
        {
            services.AddSingleton<ILoggerManager, LoggerManager>();
        }

        public static void ConfigureJwtAuthentication(this IServiceCollection services, IConfiguration configuration, IWebHostEnvironment environment)
        {
            services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
            }).AddJwtBearer(options =>
            {
                options.SaveToken = true;
                // Enable HTTPS metadata validation in production
                options.RequireHttpsMetadata = !environment.IsDevelopment();
                options.TokenValidationParameters = new TokenValidationParameters()
                {
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidateLifetime = true,
                    ValidateIssuerSigningKey = true,
                    ClockSkew = TimeSpan.Zero, // Reduce clock skew tolerance
                    ValidAudience = configuration["JWT:ValidAudience"],
                    ValidIssuer = configuration["JWT:ValidIssuer"],
                    IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(configuration["JWT:Secret"]))
                };
            });
        }

        public static void ConfigureRateLimiting(this IServiceCollection services)
        {
            services.AddRateLimiter(options =>
            {
                // Global rate limiting policy
                options.GlobalLimiter = PartitionedRateLimiter.Create<HttpContext, string>(httpContext =>
                    RateLimitPartition.GetFixedWindowLimiter(
                        partitionKey: httpContext.User.Identity?.Name ?? httpContext.Connection.RemoteIpAddress?.ToString() ?? "anonymous",
                        factory: partition => new FixedWindowRateLimiterOptions
                        {
                            AutoReplenishment = true,
                            PermitLimit = 100,
                            Window = TimeSpan.FromMinutes(1)
                        }));

                // Specific policy for authentication endpoints
                options.AddPolicy("AuthPolicy", httpContext =>
                    RateLimitPartition.GetFixedWindowLimiter(
                        partitionKey: httpContext.Connection.RemoteIpAddress?.ToString() ?? "anonymous",
                        factory: partition => new FixedWindowRateLimiterOptions
                        {
                            AutoReplenishment = true,
                            PermitLimit = 5,
                            Window = TimeSpan.FromMinutes(1)
                        }));

                // Policy for API endpoints
                options.AddPolicy("ApiPolicy", httpContext =>
                    RateLimitPartition.GetFixedWindowLimiter(
                        partitionKey: httpContext.User.Identity?.Name ?? httpContext.Connection.RemoteIpAddress?.ToString() ?? "anonymous",
                        factory: partition => new FixedWindowRateLimiterOptions
                        {
                            AutoReplenishment = true,
                            PermitLimit = 60,
                            Window = TimeSpan.FromMinutes(1)
                        }));

                options.RejectionStatusCode = StatusCodes.Status429TooManyRequests;
            });
        }

        public static void ConfigureApiVersioning(this IServiceCollection services)
        {
            services.AddApiVersioning(options =>
            {
                options.AssumeDefaultVersionWhenUnspecified = true;
                options.DefaultApiVersion = new ApiVersion(1, 0);
                options.ApiVersionReader = ApiVersionReader.Combine(
                    new UrlSegmentApiVersionReader(),
                    new HeaderApiVersionReader("X-Version"),
                    new QueryStringApiVersionReader("version")
                );
            });

            services.AddVersionedApiExplorer(setup =>
            {
                setup.GroupNameFormat = "'v'VVV";
                setup.SubstituteApiVersionInUrl = true;
            });
        }

        public static void ConfigureSwagger(this IServiceCollection services)
        {
            services.AddEndpointsApiExplorer();
            services.AddSwaggerGen(options =>
            {
                // API Information
                options.SwaggerDoc("v1", new OpenApiInfo
                {
                    Version = "v1.0",
                    Title = "SRJ.ktech API",
                    Description = "A comprehensive API for the SRJ.ktech application providing student application management, payment processing, and integration services.",
                    Contact = new OpenApiContact
                    {
                        Name = "SRJ.ktech Support",
                        Email = "<EMAIL>",
                        Url = new Uri("https://ktech.edu.kw")
                    },
                    License = new OpenApiLicense
                    {
                        Name = "Private License",
                        Url = new Uri("https://ktech.edu.kw/license")
                    }
                });

                // Include XML comments
                var xmlFile = $"{System.Reflection.Assembly.GetExecutingAssembly().GetName().Name}.xml";
                var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
                if (File.Exists(xmlPath))
                {
                    options.IncludeXmlComments(xmlPath);
                }

                // Security Definition
                options.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
                {
                    Description = @"JWT Authorization header using the Bearer scheme.
                                  Enter 'Bearer' [space] and then your token in the text input below.
                                  Example: 'Bearer 12345abcdef'",
                    Name = "Authorization",
                    In = ParameterLocation.Header,
                    Type = SecuritySchemeType.ApiKey,
                    Scheme = "Bearer"
                });

                options.AddSecurityRequirement(new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme,
                                Id = "Bearer"
                            },
                            Scheme = "oauth2",
                            Name = "Bearer",
                            In = ParameterLocation.Header
                        },
                        new List<string>()
                    }
                });

                // Custom operation filters
                options.OperationFilter<SwaggerDefaultValues>();
                options.DocumentFilter<SwaggerVersioningDocumentFilter>();

                // Enable annotations
                options.EnableAnnotations();

                // Custom schema filters
                options.SchemaFilter<SwaggerExcludeFilter>();
            });
        }

        public static IServiceCollection AddControllersWithCustomJsonOptions(this IServiceCollection services)
        {
            services.AddControllers().AddJsonOptions(opt =>
            {
                var enumConverter = new JsonStringEnumConverter(JsonNamingPolicy.CamelCase);
                opt.JsonSerializerOptions.Converters.Add(enumConverter);
            });

            return services;
        }
    }
}