﻿using SRJ.CommonCore.Interfaces;
using SRJ.DataAccess.Enums;
using System.ComponentModel.DataAnnotations;
using SRJ.DataAccess.Enums.Application;
using Newtonsoft.Json;
using Microsoft.AspNetCore.Mvc;

namespace SRJ.CommonCore.ViewModels.Application
{
    public class ApplicationFormVM : IEntityViewModel, IPaymentBaseEntity
    {
        public long Id { get; set; }

        // IPaymentBaseEntity properties
        [Required]
        [Display(Name = "Student Name")]
        public string? Name { get; set; }

        [Required]
        [Remote("CheckCivilIdExists", "UserRole", HttpMethod = "GET", ErrorMessage = "Civil ID already exists")]
        [Display(Name = "Civil Id")]
        public string? CivilId { get; set; }

        [Required]
        [Display(Name = "Mobile Number")]
        public string? MobileNumber { get; set; }

        [EmailAddress]
        [Display(Name = "Student Email")]
        public string? Email { get; set; }

        [Display(Name = "Item Name")]
        public string? ItemName { get; set; }

        [Display(Name = "Cost")]
        public double? Cost { get; set; }

        [Display(Name = "InvoiceId")]
        public string? InvoiceId { get; set; }


        // Unique identifier for the lead
        public Guid? UniqueId { get; set; }

        // deal id inside the CRM / dont map
        public string? DealId { get; set; }

        // General Lead Information
        [Display(Name = "Civil Name")]
        public string? CivilName { get; set; }

        [Display(Name = "English Civil Name")]
        public string? EnglishCivilName { get; set; }

        [Display(Name = "Contact Number")]
        public string? Number { get; set; }

        [Display(Name = "Marital Status")]
        public MaritalStatus MaritalStatus { get; set; }


        // Educational Information
        [Display(Name = "Major")]
        public KtechMajor? KtechMajor { get; set; }

        [Display(Name = "Study Type")]
        public StudyType? StudyType { get; set; }

        [Display(Name = "Funding Type")]
        public FundingType? FundingType { get; set; }

        [Display(Name = "Graduate Type")]
        public GraduateType? GraduateType { get; set; }

        [Display(Name = "School Name")]
        public string? SchoolName { get; set; }

        [Display(Name = "Source")]
        public string? Source { get; set; }

        [Display(Name = "Agent")]
        public string? Agent { get; set; }

        [Display(Name = "Seat Number")]
        public string? SeatNumber { get; set; }

        [Display(Name = "Online/Campus")]
        public OnlineCampus? OnlineOrCampus { get; set; }


        // Additional Educational Information
        [Display(Name = "Qiyas Test Score")]
        public double? QiyasTestScore { get; set; }

        [Display(Name = "Qiyas Test")]
        public bool QiyasTest { get; set; }

        // Placement Test Information
        [Display(Name = "Is Grades Updated")]
        public bool? GradesUpdated { get; set; }

        [Display(Name = "Placement Test Course ID")]
        public PlacementTestCourseId? PlacementTestCourseId { get; set; }

        [Display(Name = "Placement Test User")]
        public string? PlacementTestUser { get; set; }

        [Display(Name = "Placement Test Date")]
        public DateTime? PlacementTestDate { get; set; }

        [Display(Name = "Math Test Score")]
        public double? MathTest { get; set; }

        [Display(Name = "Computer Test Score")]
        public double? ComputerTest { get; set; }

        [Display(Name = "Reading Test Score")]
        public double? ReadingTest { get; set; }

        [Display(Name = "Grammar Test Score")]
        public double? GrammarTest { get; set; }

        [Display(Name = "Writing Test Score")]
        public double? WritingTest { get; set; }

        [Display(Name = "Total Score")]
        public double? TotalScore { get; set; }

        [Display(Name = "Entry Level")]
        public EntryLevel? EntryLevel { get; set; }

        [Display(Name = "Repeated Exams")]
        public RepeatedExams? RepeatedExams { get; set; }


        // Additional Lead Information
        [Display(Name = "GPA")]
        public string? Gpa { get; set; }

        [Display(Name = "Emergency Contact 1")]
        public string? EmergencyContact1 { get; set; }

        [Display(Name = "Emergency Contact 2")]
        public string? EmergencyContact2 { get; set; }

        [Display(Name = "Gender")]
        public UserGender Gender { get; set; }

        [Display(Name = "Nationality")]
        public string? Nationality { get; set; }

        [Display(Name = "Address")]
        public string? Address { get; set; }

        [Display(Name = "Block")]
        public string? Block { get; set; }

        [Display(Name = "Street")]
        public string? Street { get; set; }

        [Display(Name = "Apartment")]
        public string? Apartment { get; set; }


        // Lead Status Information
        [Display(Name = "Application Status")]
        public ApplicationStatus? ApplicationStatus { get; set; }

        [Display(Name = "Application Stage")]
        public ApplicationStage? ApplicationStage { get; set; }

        [Display(Name = "Successful Application Submission")]
        public bool SuccessfulApplicationSubmission { get; set; }


        // Miscellaneous Information
        [Display(Name = "Comments")]
        public string? CommentBox { get; set; }

        [Display(Name = "Lost Reason")]
        public LostReason? LostReason { get; set; }

        [Display(Name = "lost Reason Other")]
        public string? LostReasonOther { get; set; }

        [Display(Name = "Reject Reason")]
        public RejectReason? RejectReason { get; set; }

        [Display(Name = "Reject Reason Other")]
        public string? RejectReasonOther { get; set; }

        [Display(Name = "Error Comments")]
        public string? ErrorCommentBox { get; set; }

        [Display(Name = "Collector Name")]
        public string? CollectorName { get; set; }

        [Display(Name = "Semester")]
        public Semester? Semester { get; set; }

        public double? Version { get; set; }

        [Display(Name = "Date of Certificate")]
        public string? DateOfCertificate { get; set; }

        [Display(Name = "PUC Email")]
        public string? PUCEmail { get; set; }

        [Display(Name = "PUC Password")]
        public string? PUCPassword { get; set; }

        [Display(Name = "Agent Updates")]
        public string? AgentUpdates { get; set; }


        // Boolean Flags
        public bool IsCCK { get; set; }
        public bool IsNew { get; set; }
        public bool EmployeeStudent { get; set; }
        public bool PhotographyDeclaration { get; set; }
        public bool CivilIdCopy { get; set; }
        public bool PassportCopy { get; set; }
        public bool FatherCivilId { get; set; }
        public bool KuwaitiMother { get; set; }
        public bool StudentBirthCertificate { get; set; }
        public bool MotherCivilId { get; set; }
        public bool HighSchoolCertificate { get; set; }
        public bool EquivalencyLetter { get; set; }
        public bool SequenceLetter { get; set; }
        public bool SpecialNeed { get; set; }
        public bool TransferStudent { get; set; }
        public bool PucFees10kdReceipt { get; set; }
        public bool TestFees15kd { get; set; }
        public bool ApplicationFees15kd { get; set; }
        public bool SeatReservationFees { get; set; }
        public bool PUCDeclaration { get; set; }
        public bool AcceptanceLetter { get; set; }
        public bool Diplomatic { get; set; }
        public bool OfficalTranscript { get; set; }
        public bool TwimcOfficialMedical { get; set; }
        public bool MedicalReport { get; set; }
        public bool TwimcFatherWorkplace { get; set; }
        public bool FatherPassport { get; set; }


        //Payment Method

        [Display(Name = "Cash Payment")]
        [Range(0, double.MaxValue, ErrorMessage = "Please enter a valid amount")]
        public decimal? CashPayment { get; set; }

        [Display(Name = "Knet Payment")]
        [Range(0, double.MaxValue, ErrorMessage = "Please enter a valid amount")]
        public decimal? KnetPayment { get; set; }

        [Display(Name = "Bank Payment")]
        [Range(0, double.MaxValue, ErrorMessage = "Please enter a valid amount")]
        public decimal? BankPayment { get; set; }

        // Calculated property to get the total of all payment methods
        public decimal TotalPayment => (CashPayment ?? 0) + (KnetPayment ?? 0) + (BankPayment ?? 0);

        public string? invoiceFilePath { get; set; }
    }
}
