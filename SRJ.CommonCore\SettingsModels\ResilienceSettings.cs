namespace SRJ.CommonCore.SettingsModels
{
    /// <summary>
    /// Configuration settings for resilience patterns
    /// </summary>
    public class ResilienceSettings
    {
        public const string SectionName = "Resilience";

        /// <summary>
        /// Retry policy settings
        /// </summary>
        public RetrySettings Retry { get; set; } = new RetrySettings();

        /// <summary>
        /// Circuit breaker settings
        /// </summary>
        public CircuitBreakerSettings CircuitBreaker { get; set; } = new CircuitBreakerSettings();

        /// <summary>
        /// Timeout settings
        /// </summary>
        public TimeoutSettings Timeout { get; set; } = new TimeoutSettings();

        /// <summary>
        /// Service-specific settings
        /// </summary>
        public Dictionary<string, ServiceResilienceSettings> Services { get; set; } = new Dictionary<string, ServiceResilienceSettings>();
    }

    public class RetrySettings
    {
        /// <summary>
        /// Maximum number of retry attempts
        /// </summary>
        public int MaxRetryAttempts { get; set; } = 3;

        /// <summary>
        /// Base delay between retries in seconds
        /// </summary>
        public double BaseDelaySeconds { get; set; } = 1.0;

        /// <summary>
        /// Backoff type (Linear, Exponential, Constant)
        /// </summary>
        public string BackoffType { get; set; } = "Exponential";

        /// <summary>
        /// Whether to use jitter to avoid thundering herd
        /// </summary>
        public bool UseJitter { get; set; } = true;

        /// <summary>
        /// Maximum delay between retries in seconds
        /// </summary>
        public double MaxDelaySeconds { get; set; } = 30.0;
    }

    public class CircuitBreakerSettings
    {
        /// <summary>
        /// Failure ratio threshold to open circuit (0.0 to 1.0)
        /// </summary>
        public double FailureRatio { get; set; } = 0.5;

        /// <summary>
        /// Sampling duration in seconds
        /// </summary>
        public double SamplingDurationSeconds { get; set; } = 30.0;

        /// <summary>
        /// Minimum throughput before circuit breaker activates
        /// </summary>
        public int MinimumThroughput { get; set; } = 5;

        /// <summary>
        /// Duration to keep circuit open in seconds
        /// </summary>
        public double BreakDurationSeconds { get; set; } = 60.0;
    }

    public class TimeoutSettings
    {
        /// <summary>
        /// Default timeout in seconds
        /// </summary>
        public double DefaultTimeoutSeconds { get; set; } = 30.0;
    }

    public class ServiceResilienceSettings
    {
        /// <summary>
        /// Service-specific retry settings
        /// </summary>
        public RetrySettings? Retry { get; set; }

        /// <summary>
        /// Service-specific circuit breaker settings
        /// </summary>
        public CircuitBreakerSettings? CircuitBreaker { get; set; }

        /// <summary>
        /// Service-specific timeout settings
        /// </summary>
        public TimeoutSettings? Timeout { get; set; }

        /// <summary>
        /// Whether this service is enabled for resilience patterns
        /// </summary>
        public bool Enabled { get; set; } = true;
    }
}
