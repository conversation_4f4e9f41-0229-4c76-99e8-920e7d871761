{
  "ConnectionStrings": {
    "DefaultConnection": "Server=.;Database=SRJ_DB;User Id=dbuser; Password=********; TrustServerCertificate=True;Max Pool Size=100;Min Pool Size=5;Connection Timeout=30;Command Timeout=30;"
    //"MySqlConnection": "server=<server_name>;database=SRJ_DB;user=<user_name>;password=<password>;",
    //"SQLiteConnection": "Data Source=SRJ_DB.db"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.EntityFrameworkCore.Database.Command": "Information"
    },
    "EnableSensitiveDataLogging": false,
    "EnableDetailedErrors": false,
    "LogRequestResponseBodies": true
  },
  "Resilience": {
    "Retry": {
      "MaxRetryAttempts": 3,
      "BaseDelaySeconds": 1.0,
      "BackoffType": "Exponential",
      "UseJitter": true,
      "MaxDelaySeconds": 30.0
    },
    "CircuitBreaker": {
      "FailureRatio": 0.5,
      "SamplingDurationSeconds": 30.0,
      "MinimumThroughput": 5,
      "BreakDurationSeconds": 60.0
    },
    "Timeout": {
      "DefaultTimeoutSeconds": 30.0
    },
    "Services": {
      "MyFatoorah": {
        "Retry": {
          "MaxRetryAttempts": 2,
          "BaseDelaySeconds": 2.0
        },
        "Timeout": {
          "DefaultTimeoutSeconds": 45.0
        }
      },
      "Moodle": {
        "Retry": {
          "MaxRetryAttempts": 3,
          "BaseDelaySeconds": 1.5
        },
        "CircuitBreaker": {
          "FailureRatio": 0.6,
          "BreakDurationSeconds": 120.0
        }
      },
      "CRM": {
        "Retry": {
          "MaxRetryAttempts": 2,
          "BaseDelaySeconds": 1.0
        },
        "Timeout": {
          "DefaultTimeoutSeconds": 20.0
        }
      }
    }
  },
  "ShareFolderPUCSettings": {
    "RootFolderPath": "\\\\fs\\PUC\\PUC FALL 2024 - ملفات الطلاب"
  },
  "JWT": {
    "ValidAudience": "http://localhost:4200",
    "ValidIssuer": "http://localhost:5000",
    "Secret": "JWTAuthenticationHIGHsecuredPasswordVVVp1OH7Xzyr"
  },
  "CorsSettings": {
    "AllowedOrigins": [
      "https://localhost:7140" // Allow requests from this URL in development
    ]
  },
  "AllowedHosts": "*",
  "MyFatoorahSettings": {
    "MyfatoorahApiKey": "yvaKZwbT6Od3dacNNFtuAeC1dqXC1xuw_wkwWhB5FKJgQUSibWjfk6rsGoHrdZewt5Y8kWjnXN2zCzCYQqI7Dv06azu4wEPuVk4kKwn3VeJ3ik_oVM-P215pk5JN7aIGciwNzDSY3zTeM6hAzLth7i-rgdAoJsrJgOh3rTjWvWZ5IE_Bi7x-Ya6_6BNMqMkzUVRGVCg8GUShVkOTm0fueD3cddxtUEVWrw52Y1cBrn3sLfTwzWdjRHxw0zI8Hij7Zxw24YXQXwVJyCDnEpEeBZKwlHreFKq8gQP46jhN_EozUJyj5vCcbPLG9GDIrl85kVPDbd15K5-AWdbQkf8tnARm2_M_VDQcXb8Kf0n6nPIkfNgAZ7IhKmdy0hNoFs5mRUVmoIn0fSUIw_SpQj8biynJwhMOsflf_2mLPvjMmi2ywwGNc6FUhJM9O71hUL9OBVzzaEanTgc_8S1AG_0nhS3NsdboIO3ghGqPuUrx_5da4LPAnHItHVIRmceeyH4qNG19LRUFYfrPCCMS0F3oCRUomnTCgjif-9Ib2OO0xBH9s0nSN4bHG85SlEzusWnbBf4KUO8MHFjOpfOJn577sJJmcmATnLjzIs7QVYyzCkYSIPZXmRHoN8IZpnCRHudYeYD_7vGIW1XP-LskHysLn45Wy60C8mjdxv93XfAzNICNRmDNTMMND0_ps9AYaMzjDmoh_Q",
    "MyfatoorahBaseUrl": "https://apitest.myfatoorah.com",
    "CallBackUrl": "https://localhost:7140/PaymentResponse/PaymentSuccess",
    "ErrorUrl": "https://localhost:7140/PaymentResponse/PaymentError"
  },
  "EmailSettings": {
    "From": "<EMAIL>",
    "SmtpServer": "smtp.office365.com",
    "Port": 587,
    "UseSsl": true,
    "Username": "<EMAIL>",
    "Password": "Later2Know@ktech"
  },
  "MoodleApi": {
    "BaseUrl": "https://mylms.ktech.edu.kw//webservice/rest/server.php",
    "Token": "f5a94c40f21fdd7ef71ef151dd3be3d7",
    "CourseName": "PT-Fall24",
    "UserPassword": "ktech@Password1",
    "UserIdCounter": 7701
  },
  "DealApiSettings": {
    "BaseUrl": "https://crm.ktech.edu.kw/",
    "ApiKey": "2|lpyphY1jscYThl78eKJPGOP8xy4VL8Ki75EZaQVH84937dba"
  }
}
