﻿using SRJ.CommonCore.Interfaces;
using SRJ.DataAccess.Common;
using SRJ.DataAccess.Enums;
using SRJ.DataAccess.Enums.Application;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SRJ.DataAccess.Entities.Application
{
    public class ApplicationForm : BaseEntity, IPaymentBaseEntity
    {
        public ApplicationForm()
        {
            // Set the default value for ApplicationStatus
            ApplicationStatus = Enums.Application.ApplicationStatus.Pending;
        }

        // IPaymentBaseEntity properties
        public string? CivilId { get; set; }
        public string? MobileNumber { get; set; }
        public double? Cost { get; set; }
        public string? InvoiceId { get; set; }
        public string? ItemName { get; set; }


        // Unique identifier for the lead
        public Guid? UniqueId { get; set; }


        // General Lead Information
        public string? Name { get; set; }
        public string? CivilName { get; set; }
        public string? EnglishCivilName { get; set; }
        public string? Number { get; set; }
        public MaritalStatus? MaritalStatus { get; set; }


        // Educational Information
        public KtechMajor? KtechMajor { get; set; }
        public StudyType? StudyType { get; set; }
        public FundingType? FundingType { get; set; }
        public GraduateType? GraduateType { get; set; }
        public string? SchoolName { get; set; }
        public string? Source { get; set; }
        public string? Agent { get; set; }
        public string? SeatNumber { get; set; }
        public OnlineCampus? OnlineOrCampus { get; set; }

        // Additional Educational Information
        public double? QiyasTestScore { get; set; }
        public bool QiyasTest { get; set; }


        // Contact Information
        public string? Email { get; set; }


        // Placement Test Information
        public bool? GradesUpdated { get; set; }
        public PlacementTestCourseId? PlacementTestCourseId { get; set; }
        public string? PlacementTestUser { get; set; }
        public DateTime? PlacementTestDate { get; set; }
        public double? MathTest { get; set; }
        public double? ComputerTest { get; set; }
        public double? ReadingTest { get; set; }
        public double? GrammarTest { get; set; }
        public double? WritingTest { get; set; }
        public double? TotalScore { get; set; }
        public EntryLevel? EntryLevel { get; set; }
        public RepeatedExams? RepeatedExams { get; set; }


        // Additional Lead Information
        public string? Gpa { get; set; }
        public string? EmergencyContact1 { get; set; }
        public string? EmergencyContact2 { get; set; }
        public UserGender Gender { get; set; }
        public string? Nationality { get; set; }
        public string? Address { get; set; }
        public string? Block { get; set; }
        public string? Street { get; set; }
        public string? Apartment { get; set; }


        // Lead Status Information
        public ApplicationStatus? ApplicationStatus { get; set; }
        public ApplicationStage? ApplicationStage { get; set; }
        public bool SuccessfulApplicationSubmission { get; set; }


        // Miscellaneous Information
        public string? CommentBox { get; set; }
        public LostReason? LostReason { get; set; }
        public string? LostReasonOther { get; set; }
        public RejectReason? RejectReason { get; set; }
        public string? RejectReasonOther { get; set; }
        public string? ErrorCommentBox { get; set; }
        public string? CollectorName { get; set; }
        public Semester? Semester { get; set; }
        public double? Version { get; set; }
        public string? DateOfCertificate { get; set; }
        public string? PUCEmail { get; set; }
        public string? PUCPassword { get; set; }
        public string? AgentUpdates { get; set; }


        // Boolean Flags
        public bool IsCCK { get; set; }
        public bool IsNew { get; set; }
        public bool EmployeeStudent { get; set; }
        public bool PhotographyDeclaration { get; set; }
        public bool CivilIdCopy { get; set; }
        public bool PassportCopy { get; set; }
        public bool FatherCivilId { get; set; }
        public bool KuwaitiMother { get; set; }
        public bool StudentBirthCertificate { get; set; }
        public bool MotherCivilId { get; set; }
        public bool HighSchoolCertificate { get; set; }
        public bool EquivalencyLetter { get; set; }
        public bool SequenceLetter { get; set; }
        public bool SpecialNeed { get; set; }
        public bool TransferStudent { get; set; }
        public bool PucFees10kdReceipt { get; set; }
        public bool TestFees15kd { get; set; }
        public bool ApplicationFees15kd { get; set; }
        public bool SeatReservationFees { get; set; }
        public bool PUCDeclaration { get; set; }
        public bool AcceptanceLetter { get; set; }
        public bool Diplomatic { get; set; }
        public bool OfficalTranscript { get; set; }
        public bool TwimcOfficialMedical { get; set; }
        public bool MedicalReport { get; set; }
        public bool TwimcFatherWorkplace { get; set; }
        public bool FatherPassport { get; set; }
        public string? invoiceFilePath { get; set; }
    }
}
