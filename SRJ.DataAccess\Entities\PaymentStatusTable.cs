﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SRJ.DataAccess.Common;
using SRJ.CommonCore.Interfaces;

namespace SRJ.DataAccess.Entities
{
    public class PaymentStatusTable: BaseEntity, IPaymentBaseEntity
    {
        public string? Name { get; set; }
        public string? CivilId { get; set; }
        public string? MobileNumber { get; set; }
        public string? Email { get; set; }
        public string? ItemName { get; set; }
        public double? Cost { get; set; }
        public string? ktechId { get; set; }
        public string? IsSuccess { get; set; }
        public string? Message { get; set; }
        public string? ValidationErrors { get; set; }
        public string? PaymentId { get; set; }
        public string? InvoiceId { get; set; }
        public string? InvoiceStatus { get; set; }
        public string? InvoiceReference { get; set; }
        public string? CustomerReference { get; set; }
        public string? ExpiryDate { get; set; }
        public string? InvoiceValue { get; set; }
        public string? CustomerName { get; set; }
        public string? CustomerMobile { get; set; }
        public string? CustomerEmail { get; set; }
        public string? UserDefinedField { get; set; }
        public string? InvoiceItems { get; set; }
        public string? InvoiceTransactions { get; set; }
        public string? InvoiceFilePath { get; set; }
        public string? Semester { get; set; }
        public string? PaymentMethod { get; set; }
    }
}
