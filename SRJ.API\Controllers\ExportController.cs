﻿using ClosedXML.Excel;
using Microsoft.AspNetCore.Mvc;
using SRJ.DataAccess.Entities.Application;
using SRJ.DataAccess.Interfaces.MyFatoorah;
using SRJ.CommonCore.Interfaces.Application;
using SRJ.CommonCore.ViewModels.Application;
using System.IO;
using System.Threading.Tasks;

namespace SRJ.API.Controllers
{
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiController]
    public class ExportController : ControllerBase
    {
        private readonly IGenericService<ApplicationForm, ApplicationFormVM> _genericService;
        private readonly IGradeReportService _gradeReportService;

        public ExportController(IGenericService<ApplicationForm, ApplicationFormVM> genericService, IGradeReportService gradeReportService)
        {
            _genericService = genericService;
            _gradeReportService = gradeReportService;
        }

        [HttpGet("GenerateExcelFile")]
        public async Task<IActionResult> GenerateExcelFile(long id)
        {
            try
            {
                var existingRecord = await _genericService.GetByIdAsync(id);
                if (existingRecord == null)
                {
                    return NotFound("No record found for the provided Student.");
                }

                var placementTestCourseIdValue = (int)existingRecord.PlacementTestCourseId;
                if (string.IsNullOrEmpty(placementTestCourseIdValue.ToString()))
                {
                    return BadRequest("Please sync and save first");
                }

                var grades = await _gradeReportService.GetUserGradeAsync(existingRecord.CivilId, placementTestCourseIdValue.ToString());
                if (grades.grades == null || !grades.grades.Any())
                {
                    return NotFound("No grades found for the provided user.");
                }

                var workbook = new XLWorkbook();
                var worksheet = workbook.Worksheets.Add("Grades");

                var mergedCell = worksheet.Cell(1, 1);
                mergedCell.Value = "Generated from SRJ system";
                worksheet.Range(1, 1, 1, 9).Merge();
                mergedCell.Style.Font.Bold = true;
                mergedCell.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;

                worksheet.Cell(2, 1).Value = "Student Name";
                worksheet.Cell(2, 2).Value = "Civil ID";
                worksheet.Cell(2, 3).Value = "Reading Test";
                worksheet.Cell(2, 4).Value = "Grammar Test";
                worksheet.Cell(2, 5).Value = "Writing Test";
                worksheet.Cell(2, 6).Value = "Math Test";
                worksheet.Cell(2, 7).Value = "Computer Test";
                worksheet.Cell(2, 8).Value = "English Test Total";
                worksheet.Cell(2, 9).Value = "TotalScore";

                worksheet.Cell(3, 1).Value = existingRecord.Name;
                worksheet.Cell(3, 2).Value = existingRecord.CivilId;
                worksheet.Cell(3, 3).Value = grades.grades.FirstOrDefault(x => x.Itemname == "English Reading Test")?.Graderaw;
                worksheet.Cell(3, 4).Value = grades.grades.FirstOrDefault(x => x.Itemname == "English Grammar and Vocabulary Test")?.Graderaw;
                worksheet.Cell(3, 5).Value = grades.grades.FirstOrDefault(x => x.Itemname == "English Writing Test")?.Graderaw;
                worksheet.Cell(3, 6).Value = grades.grades.FirstOrDefault(x => x.Itemname == "Math Test")?.Graderaw;
                worksheet.Cell(3, 7).Value = grades.grades.FirstOrDefault(x => x.Itemname == "Computer Test")?.Graderaw;

                double readingTestValue = grades.grades.FirstOrDefault(x => x.Itemname == "English Reading Test")?.Graderaw ?? 0;
                double grammarTestValue = grades.grades.FirstOrDefault(x => x.Itemname == "English Grammar and Vocabulary Test")?.Graderaw ?? 0;
                double writingTestValue = grades.grades.FirstOrDefault(x => x.Itemname == "English Writing Test")?.Graderaw ?? 0;

                worksheet.Cell(3, 8).Value = readingTestValue + grammarTestValue + writingTestValue;
                worksheet.Cell(3, 9).Value = grades.grades.FirstOrDefault(x => x.Itemname == null)?.Graderaw;

                var range = worksheet.Range(2, 1, 3, 9);
                var table = range.CreateTable();
                table.ShowAutoFilter = true;

                worksheet.Columns().AdjustToContents();
                worksheet.Rows().AdjustToContents();

                using (var memoryStream = new MemoryStream())
                {
                    workbook.SaveAs(memoryStream);
                    var fileName = $"placement-test-{existingRecord.CivilId}.xlsx";
                    return File(memoryStream.ToArray(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }
    }
}