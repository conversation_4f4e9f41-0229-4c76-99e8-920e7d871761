﻿using Microsoft.AspNetCore.Mvc;
using SRJ.CommonCore.Services;
using SRJ.DataAccess.Entities.Application;
using SRJ.DataAccess.Interfaces.MyFatoorah;
using SRJ.CommonCore.ViewModels.Application;

namespace SRJ.API.Controllers
{
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiController]
    public class ImportedDataController : ControllerBase
    {
        private readonly IFatoorahService<ApplicationForm, ApplicationFormVM> _fatoorahService;
        private readonly IGenericService<ApplicationForm, ApplicationFormVM> _genericService;
        private readonly IUserProfileService _userProfileService;

        private readonly string _userId;

        public ImportedDataController(IFatoorahService<ApplicationForm, ApplicationFormVM> fatoorahService, IGenericService<ApplicationForm, ApplicationFormVM> genericService, IUserProfileService userProfileService)
        {
            _fatoorahService = fatoorahService;
            _genericService = genericService;
            _userProfileService = userProfileService;
            _userId = _userProfileService.GetUser().UserPrincipalName;
            _userProfileService = userProfileService;
        }

        [HttpPost("SaveSingleData")]
        public async Task<IActionResult> SaveSingleData([FromBody] ApplicationFormVM application)
        {
            try
            {
                if (application == null)
                {
                    return BadRequest("No data to save.");
                }

                var existingCivilIds = await _genericService.GetAllAsync(_userId);
                var duplicateExists = existingCivilIds.Any(a => a.CivilId == application.CivilId);

                if (duplicateExists)
                {
                    return BadRequest("Duplicate Civil ID found. The application was not saved.");
                }

                await _genericService.CreateAsync(_userId, application);

                return Ok(new { success = true, message = "Deal imported successfully." });
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Failed to save the deal. {ex.Message}");
            }
        }

        [HttpPost("SaveImportedData")]
        public async Task<IActionResult> SaveImportedData([FromBody] List<ApplicationFormVM> applications)
        {
            try
            {
                if (applications == null || !applications.Any())
                {
                    return BadRequest("No data to save.");
                }

                var existingCivilIds = (await _genericService.GetAllAsync(_userId)).Select(a => a.CivilId).ToList();
                var duplicates = new List<ApplicationFormVM>();

                foreach (var application in applications)
                {
                    if (existingCivilIds.Contains(application.CivilId))
                    {
                        duplicates.Add(application);
                        continue;
                    }

                    await _genericService.CreateAsync(_userId, application);
                }

                var duplicateCount = duplicates.Count;
                var successMessage = duplicateCount > 0 ? $"Deals imported successfully. {duplicateCount} duplicates found and skipped." : "Deals imported successfully.";

                return Ok(new { success = true, message = successMessage });
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Failed to save imported deals. {ex.Message}");
            }
        }
    }
}
