﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SRJ.DataAccess.Entities.Application;
using SRJ.CommonCore.Interfaces;
using SRJ.CommonCore.Interfaces.Application;
using SRJ.CommonCore.ViewModels.Application;
using System.Threading.Tasks;

namespace SRJ.API.Controllers
{
    /// <summary>
    /// Document management controller for handling file uploads and downloads
    /// </summary>
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiController]
    public class DocumentController : ControllerBase
    {
        private readonly IFileService _fileService;
        private readonly IGenericService<ApplicationForm, ApplicationFormVM> _genericService;
        private readonly ILogger<DocumentController> _logger;

        public DocumentController(IFileService fileService, IGenericService<ApplicationForm, ApplicationFormVM> genericService, ILogger<DocumentController> logger)
        {
            _fileService = fileService;
            _genericService = genericService;
            _logger = logger;
        }

        [HttpPost("UploadDocument")]
        [ApiExplorerSettings(IgnoreApi = true)]
        public async Task<IActionResult> UploadDocument([FromForm] long applicationId, [FromForm] string documentType, [FromForm] IFormFile file)
        {
            if (file != null && file.Length > 0)
            {
                _logger.LogInformation("Uploading document for ApplicationId: {ApplicationId}, DocumentType: {DocumentType}", applicationId, documentType);

                var application = await _genericService.GetByIdAsync(applicationId);
                if (application != null)
                {
                    _logger.LogInformation("Application found for CivilId: {CivilId}", application.CivilId);

                    _fileService.CreateFolder(application.CivilId);

                    using (var stream = file.OpenReadStream())
                    {
                        _fileService.AddFileToFolder(application.CivilId, file.FileName, documentType, stream);
                    }

                    switch (documentType)
                    {
                        case "PassportCopy":
                            application.PassportCopy = true;
                            break;
                        case "CivilIdCopy":
                            application.CivilIdCopy = true;
                            break;
                        case "FatherCivilId":
                            application.FatherCivilId = true;
                            break;
                        case "HighSchoolCertificate":
                            application.HighSchoolCertificate = true;
                            break;
                        case "AcceptanceLetter":
                            application.AcceptanceLetter = true;
                            break;
                        case "PucFees10kdReceipt":
                            application.PucFees10kdReceipt = true;
                            break;
                        case "EquivalencyLetter":
                            application.EquivalencyLetter = true;
                            break;
                        case "SequenceLetter":
                            application.SequenceLetter = true;
                            break;
                        case "OfficalTranscript":
                            application.OfficalTranscript = true;
                            break;
                        case "QiyasTest":
                            application.QiyasTest = true;
                            break;
                        case "StudentBirthCertificate":
                            application.StudentBirthCertificate = true;
                            break;
                        case "MotherCivilId":
                            application.MotherCivilId = true;
                            break;
                        case "TwimcOfficialMedical":
                            application.TwimcOfficialMedical = true;
                            break;
                        case "MedicalReport":
                            application.MedicalReport = true;
                            break;
                        case "TwimcFatherWorkplace":
                            application.TwimcFatherWorkplace = true;
                            break;
                        case "FatherPassport":
                            application.FatherPassport = true;
                            break;
                        default:
                            _logger.LogWarning("Invalid document type: {DocumentType}", documentType);
                            return BadRequest(new { success = false, message = "Invalid document type" });
                    }

                    _logger.LogInformation("Updating application status for ApplicationId: {ApplicationId}", applicationId);
                    await _genericService.UpdateAsync(application);

                    _logger.LogInformation("{DocumentType} uploaded successfully for ApplicationId: {ApplicationId}", documentType, applicationId);
                    return Ok(new { success = true, message = $"{documentType} uploaded successfully." });
                }
                else
                {
                    _logger.LogWarning("Application not found for ApplicationId: {ApplicationId}", applicationId);
                }
            }
            else
            {
                _logger.LogWarning("No file provided or file is empty for ApplicationId: {ApplicationId}, DocumentType: {DocumentType}", applicationId, documentType);
            }

            _logger.LogError("Failed to upload {DocumentType} for ApplicationId: {ApplicationId}", documentType, applicationId);
            return BadRequest(new { success = false, message = $"Failed to upload {documentType}. Please try again." });
        }
    }
}