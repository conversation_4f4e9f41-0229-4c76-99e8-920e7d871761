﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SRJ.CommonCore.Interfaces
{
    public interface IPaymentBaseEntity
    {
        public string? Name { get; set; }

        public string? CivilId { get; set; }

        public string? MobileNumber { get; set; }

        public string? Email { get; set; }

        public string? ItemName { get; set; }

        public double? Cost { get; set; }

        public string? InvoiceId { get; set; }
    }
}
