using System.Net;

namespace SRJ.API.Exceptions
{
    /// <summary>
    /// Base exception for API-specific errors
    /// </summary>
    public abstract class ApiException : Exception
    {
        public HttpStatusCode StatusCode { get; }
        public string ErrorCode { get; }

        protected ApiException(HttpStatusCode statusCode, string errorCode, string message) 
            : base(message)
        {
            StatusCode = statusCode;
            ErrorCode = errorCode;
        }

        protected ApiException(HttpStatusCode statusCode, string errorCode, string message, Exception innerException) 
            : base(message, innerException)
        {
            StatusCode = statusCode;
            ErrorCode = errorCode;
        }
    }

    /// <summary>
    /// Exception for validation errors
    /// </summary>
    public class ValidationException : ApiException
    {
        public Dictionary<string, string[]> ValidationErrors { get; }

        public ValidationException(Dictionary<string, string[]> validationErrors) 
            : base(HttpStatusCode.BadRequest, "VALIDATION_ERROR", "One or more validation errors occurred.")
        {
            ValidationErrors = validationErrors;
        }

        public ValidationException(string field, string error) 
            : base(HttpStatusCode.BadRequest, "VALIDATION_ERROR", "Validation error occurred.")
        {
            ValidationErrors = new Dictionary<string, string[]>
            {
                { field, new[] { error } }
            };
        }
    }

    /// <summary>
    /// Exception for resource not found errors
    /// </summary>
    public class NotFoundException : ApiException
    {
        public NotFoundException(string resource, object key) 
            : base(HttpStatusCode.NotFound, "RESOURCE_NOT_FOUND", $"{resource} with key '{key}' was not found.")
        {
        }

        public NotFoundException(string message) 
            : base(HttpStatusCode.NotFound, "RESOURCE_NOT_FOUND", message)
        {
        }
    }

    /// <summary>
    /// Exception for unauthorized access
    /// </summary>
    public class UnauthorizedException : ApiException
    {
        public UnauthorizedException(string message = "Unauthorized access.") 
            : base(HttpStatusCode.Unauthorized, "UNAUTHORIZED", message)
        {
        }
    }

    /// <summary>
    /// Exception for forbidden access
    /// </summary>
    public class ForbiddenException : ApiException
    {
        public ForbiddenException(string message = "Access forbidden.") 
            : base(HttpStatusCode.Forbidden, "FORBIDDEN", message)
        {
        }
    }

    /// <summary>
    /// Exception for business logic errors
    /// </summary>
    public class BusinessLogicException : ApiException
    {
        public BusinessLogicException(string message) 
            : base(HttpStatusCode.BadRequest, "BUSINESS_LOGIC_ERROR", message)
        {
        }

        public BusinessLogicException(string message, Exception innerException) 
            : base(HttpStatusCode.BadRequest, "BUSINESS_LOGIC_ERROR", message, innerException)
        {
        }
    }

    /// <summary>
    /// Exception for external service errors
    /// </summary>
    public class ExternalServiceException : ApiException
    {
        public string ServiceName { get; }

        public ExternalServiceException(string serviceName, string message) 
            : base(HttpStatusCode.BadGateway, "EXTERNAL_SERVICE_ERROR", $"External service '{serviceName}' error: {message}")
        {
            ServiceName = serviceName;
        }

        public ExternalServiceException(string serviceName, string message, Exception innerException) 
            : base(HttpStatusCode.BadGateway, "EXTERNAL_SERVICE_ERROR", $"External service '{serviceName}' error: {message}", innerException)
        {
            ServiceName = serviceName;
        }
    }

    /// <summary>
    /// Exception for rate limiting
    /// </summary>
    public class RateLimitException : ApiException
    {
        public TimeSpan RetryAfter { get; }

        public RateLimitException(TimeSpan retryAfter) 
            : base(HttpStatusCode.TooManyRequests, "RATE_LIMIT_EXCEEDED", "Rate limit exceeded. Please try again later.")
        {
            RetryAfter = retryAfter;
        }
    }
}
