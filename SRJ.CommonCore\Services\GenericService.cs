﻿using AutoMapper;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using SRJ.CommonCore.Interfaces;
using SRJ.DataAccess;
using SRJ.DataAccess.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SRJ.Logger;

namespace SRJ.CommonCore.Services
{
    public class GenericService<TEntity, TViewModel> : IGenericService<TEntity, TViewModel>
        where TEntity : BaseEntity
        where TViewModel : class, IEntityViewModel
    {
        private readonly ApplicationDbContext _context;
        private readonly IMapper _mapper;
        private readonly ILoggerManager _logger;

        public GenericService(ApplicationDbContext context, IMapper mapper, ILoggerManager logger)
        {
            _context = context;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<List<TViewModel>> GetAllAsync(string userId)
        {
            var items = await _context.Set<TEntity>()
                .ToListAsync();

            List<TViewModel> all = _mapper.Map<List<TViewModel>>(items);

            return all;
        }

        public async Task<TEntity> CreateAsync(string userId, TViewModel viewModel)
        {
            try
            {
                var entity = _mapper.Map<TEntity>(viewModel);

                if (!string.IsNullOrEmpty(userId))
                {
                    entity.UserId = await _context.Users.Where(x => x.Email == userId).Select(x => x.Id).FirstOrDefaultAsync();
                    entity.CreatedBy = userId;
                }

                entity.CreatedDate = DateTime.Now;

                _context.Set<TEntity>().Add(entity);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Successfully created entity of type {EntityType} for user {UserId}", typeof(TEntity).Name, userId);
                return entity;
            }
            catch (DbUpdateException dbEx)
            {
                _logger.LogError(dbEx, "Database error occurred while creating entity of type {EntityType} for user {UserId}", typeof(TEntity).Name, userId);
                throw;
            }
            catch (AutoMapperMappingException mapEx)
            {
                _logger.LogError(mapEx, "Mapping error occurred while creating entity of type {EntityType} for user {UserId}", typeof(TEntity).Name, userId);
                throw new InvalidOperationException("Error mapping view model to entity", mapEx);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error occurred while creating entity of type {EntityType} for user {UserId}", typeof(TEntity).Name, userId);
                throw;
            }
        }

        public async Task<TViewModel> GetByIdAsync(long id)
        {
            var entity = await _context.Set<TEntity>()
                .Include(x => x.User)
                .FirstOrDefaultAsync(x => x.Id == id);

            return _mapper.Map<TViewModel>(entity);
        }

        public async Task<TEntity> GetByInvoiceIdAsync(string invoiceId)
        {
            var entity = await _context.Set<TEntity>().FirstOrDefaultAsync(e => EF.Property<string>(e, "InvoiceId") == invoiceId);
            return _mapper.Map<TEntity>(entity);
        }

        public async Task<bool> UpdateAsync(TViewModel editViewModel)
        {
            var idValue = editViewModel.Id;

            var entity = await _context.Set<TEntity>().FindAsync(idValue);
            if (entity == null) return false;

            // Map the changes from the viewModel to the existing entity.
            _mapper.Map(editViewModel, entity);

            // Preserve the original CreatedDate and CreatedBy values.
            var originalEntity = await _context.Set<TEntity>().AsNoTracking().FirstOrDefaultAsync(e => e.Id == entity.Id);
            if (originalEntity != null)
            {
                entity.CreatedBy = originalEntity.CreatedBy;
                entity.CreatedDate = originalEntity.CreatedDate;
            }

            // Update the UpdatedAt field to the current time.
            entity.ModifiedDate = DateTime.Now;

            // Mark only modified fields as modified.
            _context.Entry(entity).State = EntityState.Modified;
            _context.Entry(entity).Property(x => x.CreatedBy).IsModified = false;
            _context.Entry(entity).Property(x => x.CreatedDate).IsModified = false;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> DeleteAsync(long id)
        {
            var entity = await _context.Set<TEntity>().FindAsync(id);
            if (entity == null) return false;
            _context.Set<TEntity>().Remove(entity);
            await _context.SaveChangesAsync();
            return true;
        }
    }
}
