﻿using AutoMapper;
using ClosedXML.Excel;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Graph;
using Microsoft.Graph.ExternalConnectors;
using Newtonsoft.Json;
using NToastNotify;
using SRJ.CommonCore.Services.MyFatoorah;
using SRJ.DataAccess.Entities.Application;
using SRJ.DataAccess.Enums.Application;
using SRJ.DataAccess.Interfaces.MyFatoorah;
using SRJ.CommonCore.Helpers;
using SRJ.CommonCore.Interfaces.Application;
using SRJ.CommonCore.Services.Application;
using SRJ.CommonCore.ViewModels.Application;
using System.ComponentModel;
using SRJ.CommonCore.ViewModels;
using SRJ.DataAccess.Entities;

namespace SRJ.Web.Controllers
{
    [DisplayName("Applications")]
    public class ApplicationController : Controller
    {
        private readonly IGenericService<ApplicationForm, ApplicationFormVM> _genericService;

        private readonly IUserProfileService _userProfileService;
        private readonly IDealsService _dealsService;
        private readonly IGradeReportService _gradeReportService;
        private readonly IGenericService<PaymentStatusTable, PaymentStatusTableVM> _paymentStatusService;
        private readonly IFileService _fileService;
        private readonly IToastNotification _toast;
        private readonly IMapper _mapper;
        private readonly ILogger<ApplicationController> _logger;
        private readonly HttpClient _httpClient;
        private readonly IConfiguration _configuration;


        private readonly string _userId;

        public ApplicationController(IGenericService<ApplicationForm, ApplicationFormVM> genericService,
            IToastNotification toast, IMapper mapper, IUserProfileService userProfileService,
            IDealsService dealsService, ILogger<ApplicationController> logger,
            IGradeReportService gradeReportService, IFileService fileService,
            IConfiguration configuration, HttpClient httpClient,
            IGenericService<PaymentStatusTable, PaymentStatusTableVM> paymentStatusService)
        {
            _genericService = genericService;
            _toast = toast;
            _mapper = mapper;
            _userProfileService = userProfileService;
            _logger = logger;
            _userId = _userProfileService.GetUser().UserPrincipalName;
            _dealsService = dealsService;
            _paymentStatusService = paymentStatusService;
            _gradeReportService = gradeReportService;
            _fileService = fileService;
            _configuration = configuration;
            _httpClient = httpClient;
        }

        public async Task<IActionResult> Index()
        {
            return View(await _genericService.GetAllAsync(_userId));
        }

        public IActionResult Create()
        {
            ApplicationFormVM vm = new()
            {
                PlacementTestCourseId = PlacementTestCourseId.PlacementTestFall20242025
            };

            ViewData["ApiBaseUrl"] = _configuration["ApiSettings:BaseUrl"];
            return View(vm);
        }

        [HttpPost]
        public async Task<IActionResult> Create(ApplicationFormVM vm)
        {
            if (ModelState.IsValid)
            {
                vm.ApplicationStatus = ApplicationStatus.Pending;

                var result = await _genericService.CreateAsync(_userId, vm);
                if (result != null)
                {
                    _toast.AddSuccessToastMessage("Application created successfully");
                    return Json(new { success = true, id = result.Id });
                }
            }
            // Provide a more specific error message if possible
            _toast.AddErrorToastMessage("Failed to create Application. Please check your input.");
            return Json(new { success = false });
        }

        public async Task<IActionResult> Details(long id)
        {
            var result = await _genericService.GetByIdAsync(id);

            if (result == null)
            {
                _toast.AddErrorToastMessage("Not Found!");
                return RedirectToAction("Index");
            }

            return View(result);
        }

        public async Task<IActionResult> Edit(long id)
        {
            var result = await _genericService.GetByIdAsync(id);

            if (result == null)
            {
                _toast.AddErrorToastMessage("Not Found!");
                return RedirectToAction("Index");
            }

            ApplicationFormVM vm = _mapper.Map<ApplicationFormVM>(result);

            if (vm.TotalScore == null && vm.GradesUpdated == null)
            {
                vm.PlacementTestCourseId = PlacementTestCourseId.PlacementTestFall20242025;
                await UpdateGradesAndEmail(vm);
            }

            // Get the invoice path
            var invoiceResponse = await GetInvoicePath(vm.CivilId, vm.MobileNumber);
            if (invoiceResponse is OkObjectResult okResult)
            {
                var invoiceData = okResult.Value as dynamic;
                if (invoiceData.success)
                {
                    ViewBag.InvoiceFilePath = invoiceData.invoiceFilePath;
                    ViewBag.PaymentMethod = invoiceData.paymentMethod;
                    ViewBag.FileApiBaseUrl = _configuration["ApiSettings:BaseUrl"];
                    if (vm.PucFees10kdReceipt == false || vm.PucFees10kdReceipt == null)
                    {
                        vm.PucFees10kdReceipt = true;
                        await _genericService.UpdateAsync(vm);
                    }
                }
            }

            ViewBag.GetFiles = _fileService.GetFiles(vm.CivilId);
            ViewData["ApiBaseUrl"] = _configuration["ApiSettings:BaseUrl"];
            ViewData["CreatedBy"] = _userId;
            return View(vm);
        }

        [HttpPost]
        public async Task<IActionResult> Edit(ApplicationFormVM vm)
        {
            if (ModelState.IsValid)
            {


                var result = await _genericService.UpdateAsync(vm);
                if (result)
                {
                    _toast.AddSuccessToastMessage("Application updated successfully");
                    return Json(new { success = true });
                }
            }
            _toast.AddErrorToastMessage("Failed to update Application. Please check your input.");
            return Json(new { success = false });
        }

        [HttpGet]
        public async Task<IActionResult> Delete(long id)
        {
            var result = await _genericService.DeleteAsync(id);

            if (result)
            {
                _toast.AddSuccessToastMessage("Application deleted successfully");
                return RedirectToAction("Index");
            }

            _toast.AddErrorToastMessage("Something went wrong!");
            return View();
        }

        public IActionResult Import()
        {
            return View();
        }

        [HttpGet]
        public async Task<IActionResult> ImportData(int draw, int start, int length, string search, string agent)
        {
            try
            {
                if (length <= 0)
                {
                    length = 15; // Default value to prevent division by zero
                }

                int page = (start / length) + 1;

                _logger.LogInformation($"Import action called with draw: {draw}, start: {start}, length: {length}, search: {search}, agent: {agent}, page: {page}");

                var response = await _dealsService.GetAllDealsAsync(length, agent, page);

                if (response != null && response.Data != null)
                {
                    _logger.LogInformation($"Fetched {response.Data.Count} deals from API.");

                    var applications = _mapper.Map<List<ApplicationFormVM>>(response.Data);

                    return Json(new
                    {
                        draw,
                        recordsTotal = response.Meta.Total,
                        recordsFiltered = response.Meta.Total, // Update if implementing search
                        data = applications
                    });
                }
                else
                {
                    _logger.LogWarning("Response from GetAllDealsAsync was null or contained no data.");
                }
            }
            catch (Exception ex)
            {
                _toast.AddErrorToastMessage("Failed to import deals.");
                _logger.LogError(ex, "Failed to import deals.");
            }

            return Json(new
            {
                draw,
                recordsTotal = 0,
                recordsFiltered = 0,
                data = new List<ApplicationFormVM>()
            });
        }

        [HttpPost]
        public async Task<IActionResult> SaveImportedData(List<ApplicationFormVM> applications)
        {
            try
            {
                if (applications == null || !applications.Any())
                {
                    _toast.AddErrorToastMessage("No data to save.");
                    return RedirectToAction("Import");
                }

                // Await the task to get the actual list of ApplicationFormVM
                var existingCivilIds = (await _genericService.GetAllAsync(_userId))
                                          .Select(a => a.CivilId).ToList();

                var duplicates = new List<ApplicationFormVM>();

                foreach (var application in applications)
                {
                    if (existingCivilIds.Contains(application.CivilId))
                    {
                        duplicates.Add(application);
                        continue;
                    }

                    await _genericService.CreateAsync(_userId, application);
                }

                if (duplicates.Any())
                {
                    _toast.AddErrorToastMessage($"{duplicates.Count} duplicates found and skipped.");
                }

                _toast.AddSuccessToastMessage("Deals imported successfully.");
                return RedirectToAction("Index");
            }
            catch (Exception ex)
            {
                _toast.AddErrorToastMessage("Failed to save imported deals.");
                _logger.LogError(ex, "Failed to save imported deals.");
                return View("Error");
            }
        }

        [HttpPost]
        public async Task<IActionResult> SaveSingleData(ApplicationFormVM application)
        {
            try
            {
                if (application == null)
                {
                    _toast.AddErrorToastMessage("No data to save.");
                    return RedirectToAction("Import");
                }

                var existingCivilIds = await _genericService.GetAllAsync(_userId);
                var duplicateExists = existingCivilIds.Any(a => a.CivilId == application.CivilId);

                if (duplicateExists)
                {
                    _toast.AddErrorToastMessage("Duplicate Civil ID found. The application was not saved.");
                    return BadRequest("Duplicate Civil ID found.");
                }

                await _genericService.CreateAsync(_userId, application);

                _toast.AddSuccessToastMessage("Deal imported successfully.");
                return Ok();
            }
            catch (Exception ex)
            {
                _toast.AddErrorToastMessage("Failed to save the deal.");
                _logger.LogError(ex, "Failed to save the deal.");
                return StatusCode(500);
            }
        }

        private async Task UpdateGradesAndEmail(ApplicationFormVM vm, bool forceUpdate = false)
        {
            if (vm.GradesUpdated != null && !forceUpdate)
            {
                return; // Skip updating grades if they have already been updated and forceUpdate is not set
            }

            try
            {
                var placementTestCourseIdValue = (int)vm.PlacementTestCourseId;
                var (grades, email) = await _gradeReportService.GetUserGradeAsync(vm.CivilId, placementTestCourseIdValue.ToString());

                if (grades == null || !grades.Any())
                {
                    vm.GradesUpdated = false; //Set the flag to false (No grades)
                    await _genericService.UpdateAsync(vm);

                    _toast.AddInfoToastMessage("No grades available for the provided Civil ID.");
                    return;
                }

                vm.PlacementTestUser = email;
                vm.ReadingTest = grades.FirstOrDefault(x => x.Itemname == "English Reading Test")?.Graderaw;
                vm.GrammarTest = grades.FirstOrDefault(x => x.Itemname == "English Grammar and Vocabulary Test")?.Graderaw;
                vm.WritingTest = grades.FirstOrDefault(x => x.Itemname == "English Writing Test")?.Graderaw;
                vm.MathTest = grades.FirstOrDefault(x => x.Itemname == "Math Test")?.Graderaw;
                vm.ComputerTest = grades.FirstOrDefault(x => x.Itemname == "Computer Test")?.Graderaw;
                vm.TotalScore = grades.FirstOrDefault(x => x.Itemname == null)?.Graderaw;

                // Assigning EntryLevel based on TotalScore using a switch expression
                vm.EntryLevel = vm.TotalScore switch
                {
                    var score when score > 70 => EntryLevel.Major,
                    var score when score >= 50 => EntryLevel.f2,
                    var score when score >= 30 => EntryLevel.f1,
                    _ => null  // Default, handling unexpected cases
                };

                vm.GradesUpdated = true; // Set the flag to true after updating grades

                await _genericService.UpdateAsync(vm);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while updating grades and email.");
                _toast.AddErrorToastMessage("An error occurred while updating the grades and email. Please try again later.");
            }
        }

        public async Task<IActionResult> GetInvoicePath(string civilId, string customerMobile)
        {
            if (string.IsNullOrEmpty(civilId) && string.IsNullOrEmpty(customerMobile))
            {
                return BadRequest(new { status = false, message = "Civil ID or Customer Mobile must be provided." });
            }

            try
            {
                var paymentStatus = await _paymentStatusService.GetAllAsync(null);

                PaymentStatusTableVM invoice = null;

                if (!string.IsNullOrEmpty(civilId))
                {
                    invoice = paymentStatus.FirstOrDefault(p => p.CivilId == civilId && p.InvoiceStatus == "Paid");
                }

                if (invoice == null && !string.IsNullOrEmpty(customerMobile))
                {
                    invoice = paymentStatus.FirstOrDefault(p => p.CustomerMobile.Contains(customerMobile) && p.InvoiceStatus == "Paid");
                }

                if (invoice == null)
                {
                    return NotFound(new { success = false, message = "Invoice not found for the provided Civil ID or Customer Mobile." });
                }

                var invoiceFilePath = invoice.InvoiceFilePath;
                var paymentMethod = invoice.PaymentMethod ?? "Bank"; // Default to Bank if PaymentMethod is empty

                return Ok(new { success = true, invoiceFilePath, paymentMethod });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while retrieving invoice path.");
                return StatusCode(500, new { success = false, message = "An error occurred. Please try again later." });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GenerateExcelFile(long Id)
        {
            try
            {
                var existingRecord = await _genericService.GetByIdAsync(Id);
                if (existingRecord == null)
                {
                    return Content("No record found for the provided Student.");
                }

                var placementTestCourseIdValue = (int)existingRecord.PlacementTestCourseId;
                if (string.IsNullOrEmpty(placementTestCourseIdValue.ToString()))
                {
                    return Content("Please sync and save first");
                }

                var grades = await _gradeReportService.GetUserGradeAsync(existingRecord.CivilId, placementTestCourseIdValue.ToString());
                if (grades.grades == null || !grades.grades.Any())
                {
                    return Content("No grades found for the provided user.");
                }

                var workbook = new XLWorkbook();
                var worksheet = workbook.Worksheets.Add("Grades");

                // Add new header
                var mergedCell = worksheet.Cell(1, 1);
                mergedCell.Value = "Generated from SRJ system";
                worksheet.Range(1, 1, 1, 9).Merge();
                mergedCell.Style.Font.Bold = true;
                mergedCell.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;

                // Shift existing headers down by one row
                worksheet.Cell(2, 1).Value = "Student Name";
                worksheet.Cell(2, 2).Value = "Civil ID";
                worksheet.Cell(2, 3).Value = "Reading Test";
                worksheet.Cell(2, 4).Value = "Grammar Test";
                worksheet.Cell(2, 5).Value = "Writing Test";
                worksheet.Cell(2, 6).Value = "Math Test";
                worksheet.Cell(2, 7).Value = "Computer Test";
                worksheet.Cell(2, 8).Value = "English Test Total";
                worksheet.Cell(2, 9).Value = "TotalScore";

                // Insert student data - shifted down by one row
                worksheet.Cell(3, 1).Value = existingRecord.Name;
                worksheet.Cell(3, 2).Value = existingRecord.CivilId;

                // Insert grade data - shifted down by one row
                worksheet.Cell(3, 3).Value = grades.grades.FirstOrDefault(x => x.Itemname == "English Reading Test")?.Graderaw;
                worksheet.Cell(3, 4).Value = grades.grades.FirstOrDefault(x => x.Itemname == "English Grammar and Vocabulary Test")?.Graderaw;
                worksheet.Cell(3, 5).Value = grades.grades.FirstOrDefault(x => x.Itemname == "English Writing Test")?.Graderaw;
                worksheet.Cell(3, 6).Value = grades.grades.FirstOrDefault(x => x.Itemname == "Math Test")?.Graderaw;
                worksheet.Cell(3, 7).Value = grades.grades.FirstOrDefault(x => x.Itemname == "Computer Test")?.Graderaw;

                // Compute the English test total (assuming it's the sum of the three components) - shifted down by one row
                var readingTest = grades.grades.FirstOrDefault(x => x.Itemname == "English Reading Test")?.Graderaw;
                var grammarTest = grades.grades.FirstOrDefault(x => x.Itemname == "English Grammar and Vocabulary Test")?.Graderaw;
                var writingTest = grades.grades.FirstOrDefault(x => x.Itemname == "English Writing Test")?.Graderaw;

                double readingTestValue = readingTest.HasValue ? readingTest.Value : 0;
                double grammarTestValue = grammarTest.HasValue ? grammarTest.Value : 0;
                double writingTestValue = writingTest.HasValue ? writingTest.Value : 0;

                worksheet.Cell(3, 8).Value = readingTestValue + grammarTestValue + writingTestValue;
                worksheet.Cell(3, 9).Value = grades.grades.FirstOrDefault(x => x.Itemname == null)?.Graderaw;

                // Creating a table
                var range = worksheet.Range(2, 1, 3, 9);
                var table = range.CreateTable();
                table.ShowAutoFilter = true;

                // Autofit columns and rows for a neat layout
                worksheet.Columns().AdjustToContents();
                worksheet.Rows().AdjustToContents();

                // Save to a MemoryStream and return as a File
                using (var memoryStream = new MemoryStream())
                {
                    workbook.SaveAs(memoryStream);
                    var fileName = $"placement-test-{existingRecord.CivilId}.xlsx";
                    return File(memoryStream.ToArray(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while generating the Excel file.");
                return Content($"An error occurred: {ex.Message}");
            }
        }

        [HttpPost]
        public async Task<IActionResult> MarkAsIncomplete(long id)
        {
            var application = await _genericService.GetByIdAsync(id);
            if (application != null)
            {
                application.SuccessfulApplicationSubmission = false;
                application.ApplicationStatus = ApplicationStatus.Incomplete;

                var result = await _genericService.UpdateAsync(application);
                if (result)
                {
                    _toast.AddSuccessToastMessage("Application marked as incomplete successfully");
                    return Json(new { success = true });
                }
            }

            _toast.AddErrorToastMessage("Failed to mark application as incomplete. Please try again.");
            return Json(new { success = false });
        }

        [HttpPost]
        public async Task<IActionResult> MarkAsComplete(long id)
        {
            var application = await _genericService.GetByIdAsync(id);
            if (application != null)
            {
                application.SuccessfulApplicationSubmission = true;
                application.ApplicationStatus = ApplicationStatus.Complete;

                var result = await _genericService.UpdateAsync(application);
                if (result)
                {
                    _toast.AddSuccessToastMessage("Application marked as complete successfully");
                    return Json(new { success = true });
                }
            }

            _toast.AddErrorToastMessage("Failed to mark application as complete. Please try again.");
            return Json(new { success = false });
        }
    }
}
