﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SRJ.CommonCore.Interfaces
{
    public interface IGenericService<TEntity, TViewModel>
        where TEntity : class
        where TViewModel : class
    {
        Task<List<TViewModel>> GetAllAsync(string userId);
        Task<TEntity> CreateAsync(string userId, TViewModel viewModel);
        Task<TViewModel> GetByIdAsync(long id);
        Task<TEntity> GetByInvoiceIdAsync(string invoiceId);
        Task<bool> UpdateAsync(TViewModel editViewModel);
        Task<bool> DeleteAsync(long id);
    }
}
