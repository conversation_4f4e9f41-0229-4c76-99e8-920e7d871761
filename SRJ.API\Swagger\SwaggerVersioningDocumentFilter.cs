using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace SRJ.API.Swagger
{
    /// <summary>
    /// Document filter to handle API versioning in Swagger documentation
    /// </summary>
    public class SwaggerVersioningDocumentFilter : IDocumentFilter
    {
        public void Apply(OpenApiDocument swaggerDoc, DocumentFilterContext context)
        {
            var pathsToModify = swaggerDoc.Paths
                .Where(p => p.Key.Contains("{version}"))
                .ToList();

            foreach (var path in pathsToModify)
            {
                swaggerDoc.Paths.Remove(path.Key);
                swaggerDoc.Paths.Add(path.Key.Replace("v{version}", swaggerDoc.Info.Version), path.Value);
            }
        }
    }
}
