using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Polly;
using Polly.CircuitBreaker;
using Polly.Retry;
using Polly.Timeout;
using SRJ.CommonCore.SettingsModels;
using System.Net;

namespace SRJ.CommonCore.Services.Resilience
{
    /// <summary>
    /// Circuit breaker state enumeration
    /// </summary>
    public enum CircuitBreakerState
    {
        Closed,
        Open,
        HalfOpen
    }

    /// <summary>
    /// Service providing resilience patterns for external API calls
    /// </summary>
    public interface IResilienceService
    {
        /// <summary>
        /// Execute HTTP request with circuit breaker and retry policies
        /// </summary>
        Task<HttpResponseMessage> ExecuteHttpRequestAsync(
            Func<Task<HttpResponseMessage>> httpCall,
            string serviceName,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Execute generic operation with circuit breaker and retry policies
        /// </summary>
        Task<T> ExecuteAsync<T>(
            Func<Task<T>> operation,
            string operationName,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get circuit breaker state for a service
        /// </summary>
        CircuitBreakerState GetCircuitBreakerState(string serviceName);
    }

    public class ResilienceService : IResilienceService
    {
        private readonly ILogger<ResilienceService> _logger;
        private readonly ResilienceSettings _settings;
        private readonly Dictionary<string, ResiliencePipeline<HttpResponseMessage>> _httpPipelines;
        private readonly Dictionary<string, ResiliencePipeline> _genericPipelines;
        private readonly Dictionary<string, CircuitBreakerState> _circuitStates;

        public ResilienceService(ILogger<ResilienceService> logger, IOptions<ResilienceSettings> settings)
        {
            _logger = logger;
            _settings = settings.Value;
            _httpPipelines = new Dictionary<string, ResiliencePipeline<HttpResponseMessage>>();
            _genericPipelines = new Dictionary<string, ResiliencePipeline>();
            _circuitStates = new Dictionary<string, CircuitBreakerState>();
        }

        public async Task<HttpResponseMessage> ExecuteHttpRequestAsync(
            Func<Task<HttpResponseMessage>> httpCall,
            string serviceName,
            CancellationToken cancellationToken = default)
        {
            var pipeline = GetOrCreateHttpPipeline(serviceName);
            
            try
            {
                return await pipeline.ExecuteAsync(async (ct) =>
                {
                    _logger.LogDebug("Executing HTTP request for service: {ServiceName}", serviceName);
                    return await httpCall();
                }, cancellationToken);
            }
            catch (BrokenCircuitException ex)
            {
                _logger.LogWarning("Circuit breaker is open for service: {ServiceName}. {Message}", 
                    serviceName, ex.Message);
                throw new ServiceUnavailableException(serviceName, "Service is temporarily unavailable due to repeated failures", ex);
            }
            catch (TimeoutRejectedException ex)
            {
                _logger.LogWarning("Request timeout for service: {ServiceName}. {Message}", 
                    serviceName, ex.Message);
                throw new ServiceTimeoutException(serviceName, "Request timed out", ex);
            }
        }

        public async Task<T> ExecuteAsync<T>(
            Func<Task<T>> operation,
            string operationName,
            CancellationToken cancellationToken = default)
        {
            var pipeline = GetOrCreateGenericPipeline(operationName);
            
            try
            {
                return await pipeline.ExecuteAsync(async (ct) =>
                {
                    _logger.LogDebug("Executing operation: {OperationName}", operationName);
                    return await operation();
                }, cancellationToken);
            }
            catch (BrokenCircuitException ex)
            {
                _logger.LogWarning("Circuit breaker is open for operation: {OperationName}. {Message}", 
                    operationName, ex.Message);
                throw new ServiceUnavailableException(operationName, "Operation is temporarily unavailable due to repeated failures", ex);
            }
            catch (TimeoutRejectedException ex)
            {
                _logger.LogWarning("Operation timeout: {OperationName}. {Message}", 
                    operationName, ex.Message);
                throw new ServiceTimeoutException(operationName, "Operation timed out", ex);
            }
        }

        public CircuitBreakerState GetCircuitBreakerState(string serviceName)
        {
            return _circuitStates.GetValueOrDefault(serviceName, CircuitBreakerState.Closed);
        }

        private ResiliencePipeline<HttpResponseMessage> GetOrCreateHttpPipeline(string serviceName)
        {
            if (_httpPipelines.TryGetValue(serviceName, out var existingPipeline))
            {
                return existingPipeline;
            }

            var serviceSettings = GetServiceSettings(serviceName);
            var retrySettings = serviceSettings.Retry ?? _settings.Retry;
            var circuitBreakerSettings = serviceSettings.CircuitBreaker ?? _settings.CircuitBreaker;
            var timeoutSettings = serviceSettings.Timeout ?? _settings.Timeout;

            var pipeline = new ResiliencePipelineBuilder<HttpResponseMessage>()
                .AddTimeout(TimeSpan.FromSeconds(timeoutSettings.DefaultTimeoutSeconds))
                .AddRetry(new RetryStrategyOptions<HttpResponseMessage>
                {
                    MaxRetryAttempts = retrySettings.MaxRetryAttempts,
                    Delay = TimeSpan.FromSeconds(retrySettings.BaseDelaySeconds),
                    BackoffType = GetBackoffType(retrySettings.BackoffType),
                    UseJitter = retrySettings.UseJitter,
                    ShouldHandle = new PredicateBuilder<HttpResponseMessage>()
                        .Handle<HttpRequestException>()
                        .Handle<TaskCanceledException>()
                        .HandleResult(response => !IsSuccessStatusCode(response)),
                    OnRetry = args =>
                    {
                        _logger.LogWarning("Retry attempt {AttemptNumber} for service {ServiceName}. Outcome: {Outcome}",
                            args.AttemptNumber, serviceName, args.Outcome);
                        return ValueTask.CompletedTask;
                    }
                })
                .AddCircuitBreaker(new CircuitBreakerStrategyOptions<HttpResponseMessage>
                {
                    FailureRatio = circuitBreakerSettings.FailureRatio,
                    SamplingDuration = TimeSpan.FromSeconds(circuitBreakerSettings.SamplingDurationSeconds),
                    MinimumThroughput = circuitBreakerSettings.MinimumThroughput,
                    BreakDuration = TimeSpan.FromSeconds(circuitBreakerSettings.BreakDurationSeconds),
                    ShouldHandle = new PredicateBuilder<HttpResponseMessage>()
                        .Handle<HttpRequestException>()
                        .Handle<TaskCanceledException>()
                        .HandleResult(response => !IsSuccessStatusCode(response)),
                    OnOpened = args =>
                    {
                        _circuitStates[serviceName] = CircuitBreakerState.Open;
                        _logger.LogError("Circuit breaker opened for service {ServiceName}. Outcome: {Outcome}",
                            serviceName, args.Outcome);
                        return ValueTask.CompletedTask;
                    },
                    OnClosed = args =>
                    {
                        _circuitStates[serviceName] = CircuitBreakerState.Closed;
                        _logger.LogInformation("Circuit breaker closed for service {ServiceName}",
                            serviceName);
                        return ValueTask.CompletedTask;
                    },
                    OnHalfOpened = args =>
                    {
                        _circuitStates[serviceName] = CircuitBreakerState.HalfOpen;
                        _logger.LogInformation("Circuit breaker half-opened for service {ServiceName}",
                            serviceName);
                        return ValueTask.CompletedTask;
                    }
                })
                .Build();

            _httpPipelines[serviceName] = pipeline;
            _circuitStates[serviceName] = CircuitBreakerState.Closed;
            
            return pipeline;
        }

        private ResiliencePipeline GetOrCreateGenericPipeline(string operationName)
        {
            if (_genericPipelines.TryGetValue(operationName, out var existingPipeline))
            {
                return existingPipeline;
            }

            var pipeline = new ResiliencePipelineBuilder()
                .AddTimeout(TimeSpan.FromSeconds(30))
                .AddRetry(new RetryStrategyOptions
                {
                    MaxRetryAttempts = 3,
                    Delay = TimeSpan.FromSeconds(1),
                    BackoffType = DelayBackoffType.Exponential,
                    UseJitter = true,
                    ShouldHandle = new PredicateBuilder()
                        .Handle<HttpRequestException>()
                        .Handle<TaskCanceledException>()
                        .Handle<TimeoutException>(),
                    OnRetry = args =>
                    {
                        _logger.LogWarning("Retry attempt {AttemptNumber} for operation {OperationName}. Exception: {Exception}",
                            args.AttemptNumber, operationName, args.Outcome.Exception?.Message);
                        return ValueTask.CompletedTask;
                    }
                })
                .AddCircuitBreaker(new CircuitBreakerStrategyOptions
                {
                    FailureRatio = 0.5,
                    SamplingDuration = TimeSpan.FromSeconds(30),
                    MinimumThroughput = 5,
                    BreakDuration = TimeSpan.FromSeconds(60),
                    ShouldHandle = new PredicateBuilder()
                        .Handle<HttpRequestException>()
                        .Handle<TaskCanceledException>()
                        .Handle<TimeoutException>(),
                    OnOpened = args =>
                    {
                        _circuitStates[operationName] = CircuitBreakerState.Open;
                        _logger.LogError("Circuit breaker opened for operation {OperationName}. Exception: {Exception}",
                            operationName, args.Outcome.Exception?.Message);
                        return ValueTask.CompletedTask;
                    },
                    OnClosed = args =>
                    {
                        _circuitStates[operationName] = CircuitBreakerState.Closed;
                        _logger.LogInformation("Circuit breaker closed for operation {OperationName}",
                            operationName);
                        return ValueTask.CompletedTask;
                    },
                    OnHalfOpened = args =>
                    {
                        _circuitStates[operationName] = CircuitBreakerState.HalfOpen;
                        _logger.LogInformation("Circuit breaker half-opened for operation {OperationName}",
                            operationName);
                        return ValueTask.CompletedTask;
                    }
                })
                .Build();

            _genericPipelines[operationName] = pipeline;
            _circuitStates[operationName] = CircuitBreakerState.Closed;
            
            return pipeline;
        }

        private static bool IsSuccessStatusCode(HttpResponseMessage response)
        {
            return response.IsSuccessStatusCode &&
                   response.StatusCode != HttpStatusCode.RequestTimeout &&
                   response.StatusCode != HttpStatusCode.TooManyRequests;
        }

        private ServiceResilienceSettings GetServiceSettings(string serviceName)
        {
            return _settings.Services.GetValueOrDefault(serviceName, new ServiceResilienceSettings());
        }

        private static DelayBackoffType GetBackoffType(string backoffType)
        {
            return backoffType.ToLowerInvariant() switch
            {
                "linear" => DelayBackoffType.Linear,
                "exponential" => DelayBackoffType.Exponential,
                "constant" => DelayBackoffType.Constant,
                _ => DelayBackoffType.Exponential
            };
        }
    }

    /// <summary>
    /// Exception thrown when a service is unavailable due to circuit breaker
    /// </summary>
    public class ServiceUnavailableException : Exception
    {
        public string ServiceName { get; }

        public ServiceUnavailableException(string serviceName, string message) : base(message)
        {
            ServiceName = serviceName;
        }

        public ServiceUnavailableException(string serviceName, string message, Exception innerException) : base(message, innerException)
        {
            ServiceName = serviceName;
        }
    }

    /// <summary>
    /// Exception thrown when a service operation times out
    /// </summary>
    public class ServiceTimeoutException : Exception
    {
        public string ServiceName { get; }

        public ServiceTimeoutException(string serviceName, string message) : base(message)
        {
            ServiceName = serviceName;
        }

        public ServiceTimeoutException(string serviceName, string message, Exception innerException) : base(message, innerException)
        {
            ServiceName = serviceName;
        }
    }
}
