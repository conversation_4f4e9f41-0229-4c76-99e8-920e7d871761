﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using SRJ.DataAccess.Entities;
using SRJ.DataAccess.Entities.Application;
using SRJ.DataAccess.Interfaces.MyFatoorah;
using SRJ.CommonCore.Interfaces.Application;
using SRJ.CommonCore.ViewModels;
using SRJ.CommonCore.ViewModels.Application;
using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using SRJ.DataAccess.Enums.Application;
using Newtonsoft.Json.Linq;
using SRJ.CommonCore.Services.MyFatoorah;
using SRJ.CommonCore.Interfaces;
using SRJ.CommonCore.Interfaces.Account;

namespace SRJ.API.Controllers
{
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiController]
    public class PaymentController : ControllerBase
    {
        private readonly IFatoorahService<ApplicationForm, ApplicationFormVM> _fatoorahService;
        private readonly IGenericService<ApplicationForm, ApplicationFormVM> _genericService;
        private readonly IGenericService<PaymentStatusTable, PaymentStatusTableVM> _paymentStatusService;
        private readonly ILogger<PaymentController> _logger;
        private readonly ICreateInvoiceService _createInvoiceService;
        private readonly IAccount _accountService;

        public PaymentController(
            IFatoorahService<ApplicationForm, ApplicationFormVM> fatoorahService,
            IGenericService<ApplicationForm, ApplicationFormVM> genericService,
            IGenericService<PaymentStatusTable, PaymentStatusTableVM> paymentStatusService,
            ILogger<PaymentController> logger,
            ICreateInvoiceService createInvoiceService,
            IAccount accountService)
        {
            _fatoorahService = fatoorahService;
            _genericService = genericService;
            _paymentStatusService = paymentStatusService;
            _logger = logger;
            _createInvoiceService = createInvoiceService;
            _accountService = accountService;
        }

        [HttpPost("SendWhatsAppPaymentLink")]
        public async Task<IActionResult> SendWhatsAppPaymentLink([FromBody] PaymentRequestModel request)
        {
            if (request.UserId <= 0 || request.TotalCost == null || string.IsNullOrEmpty(request.ItemName))
            {
                return BadRequest(new { success = false, message = "Invalid parameters." });
            }

            try
            {
                var paymentRequest = await _genericService.GetByIdAsync(request.UserId);

                if (paymentRequest == null)
                {
                    return NotFound(new { success = false, message = "User not found." });
                }

                paymentRequest.Cost = (double)request.TotalCost;
                paymentRequest.ItemName = request.ItemName;

                var paymentLinkResponse = await _fatoorahService.Payment(paymentRequest);

                if (paymentLinkResponse != null)
                {
                    var paymentLinkData = JsonConvert.DeserializeObject<dynamic>(paymentLinkResponse);
                    var paymentLink = (string)paymentLinkData?.Data?.InvoiceURL;

                    // Update InvoiceId in ApplicationForm
                    paymentRequest.InvoiceId = paymentLinkData?.Data?.InvoiceId;

                    var itemDetails = JObject.Parse(paymentRequest.ItemName);
                    var items = itemDetails["items"].ToString();

                    // Save the payment status to the database
                    var paymentStatus = new PaymentStatusTableVM
                    {
                        Name = paymentRequest.Name,
                        CivilId = paymentRequest.CivilId,
                        MobileNumber = paymentRequest.MobileNumber,
                        Email = paymentRequest.Email,
                        ItemName = items,
                        Cost = double.TryParse(paymentRequest.Cost.ToString(), out double cost) ? cost : (double?)null,
                        ktechId = paymentRequest.CivilId,
                        IsSuccess = "true",
                        Message = "Payment link generated successfully",
                        PaymentId = paymentLinkData?.Data?.PaymentId,
                        InvoiceId = paymentLinkData?.Data?.InvoiceId,
                        InvoiceStatus = "Pending",
                        InvoiceReference = paymentLinkData?.Data?.InvoiceReference,
                        CustomerReference = paymentLinkData?.Data?.CustomerReference,
                        ExpiryDate = DateTime.Now.AddHours(1).ToString("yyyy-MM-ddTHH:mm:ssZ"),
                        InvoiceValue = paymentRequest.Cost.ToString(),
                        CustomerName = paymentRequest.Name,
                        CustomerMobile = paymentRequest.MobileNumber,
                        CustomerEmail = paymentRequest.Email,
                        UserDefinedField = paymentRequest.ItemName,
                        InvoiceItems = JsonConvert.SerializeObject(paymentLinkData?.Data?.InvoiceItems),
                        InvoiceTransactions = JsonConvert.SerializeObject(paymentLinkData?.Data?.InvoiceTransactions),
                        Semester = Semester.PUC_Fall2024.ToString(),
                        PaymentMethod = request.PaymentMethod // Set the payment method
                    };

                    // Save the updated ApplicationForm
                    await _genericService.UpdateAsync(paymentRequest);

                    await _paymentStatusService.CreateAsync(request.CreatedBy, paymentStatus);

                    if (!string.IsNullOrEmpty(paymentLink))
                    {
                        var message = $"Hello! If you need any assistance, let us know. Good luck! Here is your payment link: {paymentLink}";
                        var whatsappUrl = $"https://wa.me/965{paymentRequest.MobileNumber}?text={Uri.EscapeDataString(message)}";

                        return Ok(new { success = true, whatsappUrl });
                    }
                }

                return BadRequest(new { success = false, message = "Failed to generate payment link." });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while sending WhatsApp payment link.");
                return StatusCode(500, new { success = false, message = "An error occurred. Please try again later." });
            }
        }

        [HttpGet("GetPaymentStatusByInvoiceId/{invoiceId}")]
        public async Task<IActionResult> GetPaymentStatusByInvoiceId(string invoiceId)
        {
            try
            {
                var paymentStatusResponse = await _fatoorahService.GetPaymentStatusByInvoiceId(invoiceId);

                if (string.IsNullOrEmpty(paymentStatusResponse))
                {
                    return NotFound(new { success = false, message = "Payment status not found." });
                }

                // Parse the response to JObject
                var responseObject = JObject.Parse(paymentStatusResponse);

                // Extract the 'Data' object from the response
                var data = responseObject["Data"];

                // Deserialize InvoiceTransactions
                var invoiceTransactions = data["InvoiceTransactions"].ToObject<List<InvoiceTransaction>>();

                // Deserialize InvoiceItems
                var invoiceItems = data["InvoiceItems"].ToObject<List<InvoiceItem>>();

                // Handle UserDefinedField
                UserDefinedField userDefinedField = null;
                var userDefinedFieldString = data["UserDefinedField"].ToString();

                if (IsValidJson(userDefinedFieldString))
                {
                    userDefinedField = JsonConvert.DeserializeObject<UserDefinedField>(userDefinedFieldString);
                }
                else
                {
                    // If not valid JSON, handle as a plain string
                    userDefinedField = new UserDefinedField
                    {
                        Items = userDefinedFieldString
                    };
                }

                // Create the formatted response
                var formattedResponse = new
                {
                    success = true,
                    data = new
                    {
                        IsSuccess = responseObject["IsSuccess"].Value<bool>(),
                        Message = responseObject["Message"]?.ToString() ?? string.Empty,
                        ValidationErrors = responseObject["ValidationErrors"]?.ToString() ?? string.Empty,
                        Data = new
                        {
                            InvoiceId = data["InvoiceId"].Value<int>(),
                            InvoiceStatus = data["InvoiceStatus"].ToString(),
                            InvoiceReference = data["InvoiceReference"].ToString(),
                            CustomerReference = data["CustomerReference"].ToString(),
                            CreatedDate = data["CreatedDate"].ToString(),
                            ExpiryDate = data["ExpiryDate"].ToString(),
                            ExpiryTime = data["ExpiryTime"].ToString(),
                            InvoiceValue = data["InvoiceValue"].Value<decimal>(),
                            Comments = data["Comments"]?.ToString() ?? string.Empty,
                            CustomerName = data["CustomerName"].ToString(),
                            CustomerMobile = data["CustomerMobile"].ToString(),
                            CustomerEmail = data["CustomerEmail"]?.ToString() ?? string.Empty,
                            UserDefinedField = userDefinedField,
                            InvoiceDisplayValue = data["InvoiceDisplayValue"].ToString(),
                            DueDeposit = data["DueDeposit"].Value<decimal>(),
                            DepositStatus = data["DepositStatus"].ToString(),
                            InvoiceItems = invoiceItems,
                            InvoiceTransactions = invoiceTransactions,
                            Suppliers = data["Suppliers"]
                        }
                    }
                };

                return Ok(formattedResponse);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while retrieving payment status by invoice ID.");
                return StatusCode(500, new { success = false, message = "An error occurred. Please try again later." });
            }
        }

        [HttpPost("ManualPayment")]
        public async Task<IActionResult> ManualPayment([FromBody] PaymentRequestModel request)
        {
            if (request.UserId <= 0 || request.TotalCost == null || string.IsNullOrEmpty(request.ItemName) || string.IsNullOrEmpty(request.PaymentMethod))
            {
                return BadRequest(new { success = false, message = "Invalid parameters." });
            }

            try
            {
                var paymentRequest = await _genericService.GetByIdAsync(request.UserId);

                if (paymentRequest == null)
                {
                    return NotFound(new { success = false, message = "User not found." });
                }

                paymentRequest.Cost = (double)request.TotalCost;
                paymentRequest.ItemName = request.ItemName;

                // Save the payment status to the database
                var paymentStatus = new PaymentStatusTableVM
                {
                    Name = paymentRequest.Name,
                    CivilId = paymentRequest.CivilId,
                    MobileNumber = paymentRequest.MobileNumber,
                    Email = paymentRequest.Email,
                    ItemName = request.ItemName,
                    Cost = (double)request.TotalCost,
                    ktechId = paymentRequest.CivilId,
                    IsSuccess = "true",
                    Message = "Manual payment processed successfully",
                    PaymentId = null,
                    InvoiceId = null,
                    InvoiceStatus = "Paid",
                    InvoiceReference = null,
                    CustomerReference = null,
                    ExpiryDate = DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ssZ"),
                    InvoiceValue = request.TotalCost.ToString(),
                    CustomerName = paymentRequest.Name,
                    CustomerMobile = paymentRequest.MobileNumber,
                    CustomerEmail = paymentRequest.Email,
                    UserDefinedField = request.ItemName,
                    InvoiceItems = null,
                    InvoiceTransactions = null,
                    Semester = Semester.PUC_Fall2024.ToString(),
                    PaymentMethod = request.PaymentMethod // Set the payment method
                };

                var vm = await _paymentStatusService.CreateAsync(request.CreatedBy, paymentStatus);

                var agent = await _accountService.GetUser(vm.UserId.Value);

                paymentStatus.Id = vm.Id;

                var saveFilePath = await _createInvoiceService.InvoiceAsync(paymentStatus, $"{agent.FirstName} {agent.LastName}");

                // Save the updated ApplicationForm
                paymentRequest.invoiceFilePath = saveFilePath;
                paymentRequest.InvoiceId = paymentStatus.InvoiceId;

                await _genericService.UpdateAsync(paymentRequest);

                return Ok(new { success = true, message = "Manual payment processed successfully." });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while processing manual payment.");
                return StatusCode(500, new { success = false, message = "An error occurred. Please try again later." });
            }
        }

        [HttpPost("GenerateInvoice")]
        public async Task<IActionResult> GenerateInvoice([FromBody] PaymentStatusTableVM model)
        {
            try
            {
                string agentFullName = model.Collector;

                var filePath = await _createInvoiceService.InvoiceAsync(model, agentFullName);

                if (!string.IsNullOrEmpty(filePath))
                {
                    // Return success response with the file path
                    return Ok(new { success = true, message = "Invoice generated successfully.", invoiceUrl = $"/Files/{filePath}" });
                }

                return BadRequest(new { success = false, message = "Failed to generate invoice." });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while generating invoice.");
                return StatusCode(500, new { success = false, message = "An error occurred. Please try again later." });
            }
        }

        private bool IsValidJson(string strInput)
        {
            strInput = strInput.Trim();
            if ((strInput.StartsWith("{") && strInput.EndsWith("}")) || // For object
                (strInput.StartsWith("[") && strInput.EndsWith("]")))   // For array
            {
                try
                {
                    var obj = JToken.Parse(strInput);
                    return true;
                }
                catch (JsonReaderException jex)
                {
                    _logger.LogError(jex, "Error validating JSON string.");
                    return false;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error validating JSON string.");
                    return false;
                }
            }
            return false;
        }

        public class InvoiceTransaction
        {
            public string TransactionDate { get; set; }
            public string PaymentGateway { get; set; }
            public string ReferenceId { get; set; }
            public string TrackId { get; set; }
            public string TransactionId { get; set; }
            public string PaymentId { get; set; }
            public string AuthorizationId { get; set; }
            public string TransactionStatus { get; set; }
            public decimal TransationValue { get; set; }
            public decimal CustomerServiceCharge { get; set; }
            public decimal TotalServiceCharge { get; set; }
            public decimal DueValue { get; set; }
            public string PaidCurrency { get; set; }
            public decimal PaidCurrencyValue { get; set; }
            public decimal VatAmount { get; set; }
            public string IpAddress { get; set; }
            public string Country { get; set; }
            public string Currency { get; set; }
            public string Error { get; set; }
            public string CardNumber { get; set; }
            public string ErrorCode { get; set; }
        }

        public class InvoiceItem
        {
            public string ItemName { get; set; }
            public int Quantity { get; set; }
            public decimal UnitPrice { get; set; }
            public decimal? Weight { get; set; }
            public decimal? Width { get; set; }
            public decimal? Height { get; set; }
            public decimal? Depth { get; set; }
        }

        public class UserDefinedField
        {
            public string Items { get; set; }
            public Fees Fees { get; set; }
        }

        public class Fees
        {
            public string Cash { get; set; }
            public string Knet { get; set; }
            public string Online { get; set; }
        }

        public class PaymentRequestModel
        {
            public long UserId { get; set; }
            public string? ItemName { get; set; }
            public double? TotalCost { get; set; }
            public string? CreatedBy { get; set; }
            public string? PaymentMethod { get; set; } // Add PaymentMethod parameter
        }
    }
}