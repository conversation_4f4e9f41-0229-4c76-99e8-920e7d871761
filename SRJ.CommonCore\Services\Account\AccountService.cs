﻿using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using SRJ.DataAccess;
using SRJ.DataAccess.Enums;
using SRJ.DataAccess.Identity;
using SRJ.CommonCore.Interfaces;
using SRJ.CommonCore.Interfaces.Account;
using SRJ.CommonCore.ViewModels.Account;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SRJ.Logger;

namespace SRJ.CommonCore.Services.Account
{
    public class AccountService : IAccount
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<AppUser> _userManager;
        private readonly RoleManager<AppRole> _roleManager;
        private readonly ICacheService _cacheService;
        private readonly ILoggerManager _logger;

        public AccountService(ApplicationDbContext context, UserManager<AppUser> userManager, RoleManager<AppRole> roleManager, ICacheService cacheService, ILoggerManager logger)
        {
            _context = context;
            _userManager = userManager;
            _roleManager = roleManager;
            _cacheService = cacheService;
            _logger = logger;
        }

        public async Task<UserStatus> BlockUnblockUser(long userId)
        {
            try
            {
                _logger.LogInformation("Attempting to block/unblock user with ID: {UserId}", userId);

                var user = await _context.Users.FindAsync(userId);

                if (user != null)
                {
                    var previousStatus = user.Status;
                    user.Status = user.Status == UserStatus.Active ? UserStatus.Inactive : UserStatus.Active;
                    _context.Update(user);
                    await _context.SaveChangesAsync();

                    _logger.LogInformation("Successfully changed user {UserId} status from {PreviousStatus} to {NewStatus}",
                        userId, previousStatus, user.Status);

                    return user.Status;
                }

                _logger.LogWarning("User with ID {UserId} not found for block/unblock operation", userId);
                return UserStatus.Banned;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while blocking/unblocking user with ID: {UserId}", userId);
                throw;
            }
        }

        public async Task<bool> CheckEmailExists(string email)
        {
            return await _context.Users.AnyAsync(x => x.Email.ToLower() == email.ToLower());
        }

        public async Task<bool> CheckPhoneNumberExists(string phoneNumber)
        {
            return await _context.Users.AnyAsync(x => x.PhoneNumber.ToLower() == phoneNumber.ToLower());
        }

        public async Task<bool> CheckUserNameExists(string userName)
        {
            return await _context.Users.AnyAsync(u => u.UserName.ToLower() == userName.ToLower());
        }

        public async Task<bool> DeleteUser(long id)
        {
            var user = await _context.Users.FindAsync(id);
            if (user?.UserName == "adminuser")
            {
                return false;
            }

            if (user != null)
            {
                _context.Remove(user);
                await _context.SaveChangesAsync();

                // Invalidate users cache
                await _cacheService.RemoveAsync("users_list");

                return true;
            }
            return false;
        }

        public async Task<bool> EditUser(EditUserVM vm)
        {
            try
            {
                var user = await _context.Users.FirstOrDefaultAsync(x => x.Id == vm.Id);
                if (user == null) return false;

                user.Email = vm.Email;
                user.FirstName = vm.FirstName;
                user.LastName = vm.LastName;
                user.Gender = vm.Gender;

                var userRoles = await _userManager.GetRolesAsync(user);
                var rolesToAdd = new List<string> { vm.SelectedRole }.Except(userRoles).ToList();
                var rolesToRemove = userRoles.Except(new List<string> { vm.SelectedRole }).ToList();

                var result = await _userManager.UpdateAsync(user);
                if (!result.Succeeded) return false;

                if (rolesToAdd.Any())
                {
                    result = await _userManager.AddToRolesAsync(user, rolesToAdd);
                    if (!result.Succeeded) return false;
                }

                if (rolesToRemove.Any())
                {
                    result = await _userManager.RemoveFromRolesAsync(user, rolesToRemove);
                    if (!result.Succeeded) return false;
                }

                // Invalidate users cache
                await _cacheService.RemoveAsync("users_list");

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<AppRole> GetRole(long id)
        {
            return await _roleManager.FindByIdAsync(id.ToString());
        }

        public async Task<List<AppRole>> GetRoles()
        {
            const string cacheKey = "roles_list";

            var cachedRoles = await _cacheService.GetOrSetAsync(cacheKey, async () =>
            {
                return await _roleManager.Roles
                    .AsNoTracking()
                    .ToListAsync();
            }, TimeSpan.FromHours(1)); // Cache for 1 hour (roles change less frequently)

            return cachedRoles;
        }

        public async Task<UserVM> GetUser(long id)
        {
            var user = await _userManager.FindByIdAsync(id.ToString());
            if (user == null) return null;

            var roles = await _userManager.GetRolesAsync(user);
            return new UserVM
            {
                Id = user.Id,
                FirstName = user.FirstName,
                LastName = user.LastName,
                Gender = user.Gender,
                Role = roles.FirstOrDefault(),
                UserName = user.UserName,
                Email = user.Email,
                Status = user.Status,
                CreatedAt = user.CreatedAt,
            };
        }

        public async Task<UserVM> GetUserByEmail(string email)
        {
            ArgumentNullException.ThrowIfNull(email, nameof(email));

            var user = await _userManager.FindByEmailAsync(email);
            if (user == null) return null;

            var roles = await _userManager.GetRolesAsync(user);
            return new UserVM
            {
                Id = user.Id,
                FirstName = user.FirstName,
                LastName = user.LastName,
                Gender = user.Gender,
                Role = roles.FirstOrDefault(),
                UserName = user.UserName,
                Email = user.Email,
                Status = user.Status,
                CreatedAt = user.CreatedAt,
            };
        }

        public async Task<List<UserVM>> GetUsers()
        {
            const string cacheKey = "users_list";

            // Try to get from cache first
            var cachedUsers = await _cacheService.GetOrSetAsync(cacheKey, async () =>
            {
                // Use a single query with joins to avoid N+1 problem
                var usersWithRoles = await (
                    from user in _context.Users
                    join userRole in _context.UserRoles on user.Id equals userRole.UserId into userRoles
                    from ur in userRoles.DefaultIfEmpty()
                    join role in _context.Roles on ur.RoleId equals role.Id into roles
                    from r in roles.DefaultIfEmpty()
                    select new
                    {
                        User = user,
                        RoleName = r != null ? r.Name : null
                    }
                )
                .AsNoTracking()
                .ToListAsync();

                // Group by user and take the first role (if any)
                var userVMs = usersWithRoles
                    .GroupBy(x => x.User.Id)
                    .Select(g => new UserVM
                    {
                        Id = g.Key,
                        FirstName = g.First().User.FirstName,
                        LastName = g.First().User.LastName,
                        Gender = g.First().User.Gender,
                        Role = g.FirstOrDefault(x => x.RoleName != null)?.RoleName,
                        UserName = g.First().User.UserName,
                        Email = g.First().User.Email,
                        Status = g.First().User.Status,
                        CreatedAt = g.First().User.CreatedAt,
                    })
                    .ToList();

                return userVMs;
            }, TimeSpan.FromMinutes(10)); // Cache for 10 minutes

            return cachedUsers;
        }
    }
}
