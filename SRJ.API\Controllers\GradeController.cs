﻿using Microsoft.AspNetCore.Mvc;
using SRJ.CommonCore.Interfaces.Application;
using SRJ.CommonCore.ViewModels.Application;
using SRJ.API.Models;
using SRJ.DataAccess.Entities.Application;
using SRJ.DataAccess.Enums.Application;
using System.Linq;
using System.Threading.Tasks;
using SRJ.DataAccess.Interfaces.MyFatoorah;

namespace SRJ.API.Controllers
{
    /// <summary>
    /// Grade management controller for handling student grade operations and Moodle integration
    /// </summary>
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiController]
    public class GradeController : ControllerBase
    {
        private readonly IGradeReportService _gradeReportService;
        private readonly IGenericService<ApplicationForm, ApplicationFormVM> _genericService;

        public GradeController(IGradeReportService gradeReportService, IGenericService<ApplicationForm, ApplicationFormVM> genericService)
        {
            _gradeReportService = gradeReportService;
            _genericService = genericService;
        }

        [HttpPost("UpdateGradesAndEmail")]
        public async Task<IActionResult> UpdateGradesAndEmail([FromBody] ApplicationFormVM vm, bool forceUpdate = false)
        {
            if (vm.GradesUpdated != null && !forceUpdate)
            {
                return Ok(ApiResponse.SuccessResponse("Grades already updated")); // Skip updating grades if they have already been updated and forceUpdate is not set
            }

            try
            {
                var placementTestCourseIdValue = (int)vm.PlacementTestCourseId;
                var (grades, email) = await _gradeReportService.GetUserGradeAsync(vm.CivilId, placementTestCourseIdValue.ToString());

                if (grades == null || !grades.Any())
                {
                    vm.GradesUpdated = false; // Set the flag to false (No grades)
                    await _genericService.UpdateAsync(vm);

                    return NotFound(ApiResponse.ErrorResponse("No grades available for the provided Civil ID.", 404));
                }

                vm.PlacementTestUser = email;
                vm.ReadingTest = grades.FirstOrDefault(x => x.Itemname == "English Reading Test")?.Graderaw;
                vm.GrammarTest = grades.FirstOrDefault(x => x.Itemname == "English Grammar and Vocabulary Test")?.Graderaw;
                vm.WritingTest = grades.FirstOrDefault(x => x.Itemname == "English Writing Test")?.Graderaw;
                vm.MathTest = grades.FirstOrDefault(x => x.Itemname == "Math Test")?.Graderaw;
                vm.ComputerTest = grades.FirstOrDefault(x => x.Itemname == "Computer Test")?.Graderaw;
                vm.TotalScore = grades.FirstOrDefault(x => x.Itemname == null)?.Graderaw;

                vm.EntryLevel = vm.TotalScore switch
                {
                    var score when score > 70 => EntryLevel.Major,
                    var score when score >= 50 => EntryLevel.f2,
                    var score when score >= 30 => EntryLevel.f1,
                    _ => null  // Default, handling unexpected cases
                };

                vm.GradesUpdated = true; // Set the flag to true after updating grades

                await _genericService.UpdateAsync(vm);
                return Ok(vm);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"An error occurred while updating the grades and email. Please try again later. {ex.Message}");
            }
        }

        [HttpPost("PTSubmitForm")]
        public async Task<IActionResult> PTSubmitForm([FromBody] ApplicationFormVM model)
        {
            try
            {
                if (!string.IsNullOrEmpty(model.CivilId))
                {
                    await UpdateGradesAndEmail(model, forceUpdate: true);

                    if (model.TotalScore != null)
                    {
                        var updatedGradesData = new
                        {
                            model.PlacementTestUser,
                            model.ReadingTest,
                            model.GrammarTest,
                            model.WritingTest,
                            model.MathTest,
                            model.ComputerTest,
                            model.TotalScore,
                            model.EntryLevel
                        };

                        return Ok(ApiResponse<object>.SuccessResponse(updatedGradesData, "Grades updated successfully"));
                    }
                    else
                    {
                        return BadRequest(ApiResponse.ErrorResponse("No grades found for the provided user!", 400));
                    }
                }
                else
                {
                    return BadRequest(ApiResponse.ErrorResponse("Please Enter Civil Id!", 400));
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponse.ErrorResponse($"An error occurred while updating the record. {ex.Message}", 500));
            }
        }
    }
}