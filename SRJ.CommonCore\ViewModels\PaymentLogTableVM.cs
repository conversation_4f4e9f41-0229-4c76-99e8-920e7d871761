﻿using SRJ.CommonCore.Interfaces;
using System;
using System.ComponentModel.DataAnnotations;

namespace SRJ.CommonCore.ViewModels
{
    public class PaymentLogTableVM : IEntityViewModel
    {
        public long Id { get; set; }

        [Display(Name = "Ktech ID")]
        public string? ktechId { get; set; }

        [Display(Name = "Success")]
        public string? IsSuccess { get; set; }

        [Display(Name = "Message")]
        public string? Message { get; set; }

        [Display(Name = "Validation Errors")]
        public string? ValidationErrors { get; set; }

        [Display(Name = "Invoice ID")]
        public string? InvoiceId { get; set; }

        [Display(Name = "Invoice URL")]
        public string? InvoiceURL { get; set; }

        [Display(Name = "Customer Reference")]
        public string? CustomerReference { get; set; }

        [Display(Name = "Invoice URL ID")]
        public string? InvoiceURLId { get; set; }
    }
}