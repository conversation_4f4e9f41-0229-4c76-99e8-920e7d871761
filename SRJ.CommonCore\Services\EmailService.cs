﻿using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Mail;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using SRJ.DataAccess.Interfaces.MyFatoorah;
using SRJ.CommonCore.SettingsModels;

namespace SRJ.CommonCore.Services
{
    public class EmailService : IEmailService
    {
        private readonly IWebHostEnvironment _webHostEnvironment;
        private readonly MailSettings _emailSettings;

        public EmailService(IWebHostEnvironment webHostEnvironment, IOptions<MailSettings> emailSettings)
        {
            _webHostEnvironment = webHostEnvironment;
            _emailSettings = emailSettings.Value;
        }


    }
}
