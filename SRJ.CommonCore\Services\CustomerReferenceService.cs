﻿using SRJ.CommonCore.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SRJ.CommonCore.Services
{
    public class CustomerReferenceService : ICustomerReferenceService
    {
        public string GenerateCustomerReference(string id, CustomerReferenceType type)
        {
            // Determine the prefix based on the type
            var prefix = type;

            // Get the current date in a specific format (e.g., year, month, day)
            string date = DateTime.Now.ToString("yyyyMMdd");

            // Combine elements to form the reference number
            string referenceNumber = $"{prefix}-{id}-{date}";

            return referenceNumber;
        }
    }
}
