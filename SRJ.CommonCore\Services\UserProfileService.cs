﻿using Microsoft.AspNetCore.Http;
using Microsoft.Graph;
using SRJ.CommonCore.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;

namespace SRJ.CommonCore.Services
{
    public class UserProfileService : IUserProfileService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        public UserProfileService(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        public User GetUser()
        {
            ClaimsPrincipal user = _httpContextAccessor.HttpContext.User;
            var displayName = user.FindFirstValue("name");
            var preferredUsername = user.FindFirstValue("preferred_username");

            return new User
            {
                DisplayName = displayName,
                UserPrincipalName = preferredUsername
            };
        }
    }
}
